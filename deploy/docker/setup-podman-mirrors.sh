#!/bin/bash

# 设置 Podman 国内镜像源配置脚本
# 适用于加速镜像拉取

echo "正在配置 Podman 国内镜像源..."

# 创建 Podman 配置目录
mkdir -p ~/.config/containers

# 创建镜像源配置文件
cat > ~/.config/containers/registries.conf << 'EOF'
# 配置 Docker Hub 镜像源
[[registry]]
prefix = "docker.io"
location = "docker.io"

[[registry.mirror]]
location = "registry.cn-hangzhou.aliyuncs.com"
insecure = false

[[registry.mirror]]
location = "docker.mirrors.ustc.edu.cn"
insecure = false

[[registry.mirror]]
location = "hub-mirror.c.163.com"
insecure = false

# 配置其他常用镜像源
[[registry]]
prefix = "quay.io"
location = "quay.io"

[[registry.mirror]]
location = "quay.mirrors.ustc.edu.cn"
insecure = false

# 配置 gcr.io 镜像源
[[registry]]
prefix = "gcr.io"
location = "gcr.io"

[[registry.mirror]]
location = "gcr.mirrors.ustc.edu.cn"
insecure = false
EOF

echo "Podman 镜像源配置完成！"
echo "配置文件位置: ~/.config/containers/registries.conf"
echo ""
echo "您可以使用以下命令测试镜像拉取："
echo "podman pull docker.io/library/hello-world"
