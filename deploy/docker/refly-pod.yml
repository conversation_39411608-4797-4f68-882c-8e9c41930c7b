# Refly Pod 配置文件
# 将所有服务部署在一个 Pod 中，共享网络和存储

apiVersion: v1
kind: Pod
metadata:
  name: refly-pod
  labels:
    app: refly
spec:
  # 端口映射 - Pod 级别
  ports:
    - containerPort: 5700
      hostPort: 5700
      protocol: TCP
      name: web
    - containerPort: 5800
      hostPort: 5800
      protocol: TCP
      name: api
    - containerPort: 5801
      hostPort: 5801
      protocol: TCP
      name: api-ws
    - containerPort: 35432
      hostPort: 35432
      protocol: TCP
      name: postgres
    - containerPort: 36379
      hostPort: 36379
      protocol: TCP
      name: redis
    - containerPort: 38001
      hostPort: 38001
      protocol: TCP
      name: redis-ui
    - containerPort: 39000
      hostPort: 39000
      protocol: TCP
      name: minio-api
    - containerPort: 39001
      hostPort: 39001
      protocol: TCP
      name: minio-console
    - containerPort: 36333
      hostPort: 36333
      protocol: TCP
      name: qdrant
    - containerPort: 38080
      hostPort: 38080
      protocol: TCP
      name: searxng

  # 共享卷
  volumes:
    - name: db-data
      persistentVolumeClaim:
        claimName: refly-db-data
    - name: minio-data
      persistentVolumeClaim:
        claimName: refly-minio-data
    - name: redis-data
      persistentVolumeClaim:
        claimName: refly-redis-data
    - name: qdrant-data
      persistentVolumeClaim:
        claimName: refly-qdrant-data
    - name: searxng-config
      configMap:
        name: searxng-config

  containers:
    # PostgreSQL 数据库
    - name: postgres
      image: docker.io/library/postgres:16-alpine
      ports:
        - containerPort: 5432
      env:
        - name: POSTGRES_DB
          value: "refly"
        - name: POSTGRES_USER
          value: "refly"
        - name: POSTGRES_PASSWORD
          value: "test"
      volumeMounts:
        - name: db-data
          mountPath: /var/lib/postgresql/data
      livenessProbe:
        exec:
          command:
            - pg_isready
            - -U
            - refly
        initialDelaySeconds: 10
        periodSeconds: 10

    # Redis 缓存
    - name: redis
      image: docker.io/redis/redis-stack:latest
      ports:
        - containerPort: 6379
        - containerPort: 8001
      volumeMounts:
        - name: redis-data
          mountPath: /data
      livenessProbe:
        exec:
          command:
            - redis-cli
            - ping
        initialDelaySeconds: 10
        periodSeconds: 10

    # MinIO 对象存储
    - name: minio
      image: docker.io/minio/minio:RELEASE.2025-01-20T14-49-07Z
      command:
        - server
        - /data
        - --console-address
        - ":9001"
      ports:
        - containerPort: 9000
        - containerPort: 9001
      env:
        - name: MINIO_ROOT_USER
          value: "minioadmin"
        - name: MINIO_ROOT_PASSWORD
          value: "minioadmin"
      volumeMounts:
        - name: minio-data
          mountPath: /data
      livenessProbe:
        httpGet:
          path: /minio/health/live
          port: 9000
        initialDelaySeconds: 10
        periodSeconds: 30

    # Qdrant 向量数据库
    - name: qdrant
      image: docker.io/reflyai/qdrant:v1.13.1
      ports:
        - containerPort: 6333
        - containerPort: 6334
      volumeMounts:
        - name: qdrant-data
          mountPath: /qdrant/storage
      livenessProbe:
        httpGet:
          path: /healthz
          port: 6333
        initialDelaySeconds: 10
        periodSeconds: 30

    # SearXNG 搜索引擎
    - name: searxng
      image: docker.io/searxng/searxng:latest
      ports:
        - containerPort: 8080
      env:
        - name: SEARXNG_BASE_URL
          value: "https://localhost/"
        - name: UWSGI_WORKERS
          value: "4"
        - name: UWSGI_THREADS
          value: "4"
      volumeMounts:
        - name: searxng-config
          mountPath: /etc/searxng

    # Refly API 服务
    - name: api
      image: docker.io/reflyai/refly-api:latest
      ports:
        - containerPort: 5800
        - containerPort: 5801
      env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "5800"
        - name: WS_PORT
          value: "5801"
        - name: ORIGIN
          value: "http://localhost:5700"
        - name: AUTO_MIGRATE_DB_SCHEMA
          value: "1"
        - name: DATABASE_URL
          value: "postgresql://refly:test@localhost:5432/refly?schema=refly"
        - name: REDIS_HOST
          value: "localhost"
        - name: REDIS_PORT
          value: "6379"
        - name: MINIO_INTERNAL_ENDPOINT
          value: "localhost"
        - name: MINIO_EXTERNAL_ENDPOINT
          value: "localhost"
        - name: QDRANT_HOST
          value: "localhost"
        - name: SEARXNG_BASE_URL
          value: "http://localhost:8080"
        - name: JWT_SECRET
          value: "your-super-secret-jwt-key-change-this-in-production"
        - name: ENCRYPTION_KEY
          value: "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"
      livenessProbe:
        httpGet:
          path: /
          port: 5800
        initialDelaySeconds: 30
        periodSeconds: 30
      dependsOn:
        - postgres
        - redis
        - minio
        - qdrant

    # Refly Web 前端
    - name: web
      image: docker.io/reflyai/refly-web:latest
      ports:
        - containerPort: 80
      env:
        - name: API_URL
          value: "/api"
        - name: COLLAB_URL
          value: "/collab"
        - name: STATIC_PUBLIC_ENDPOINT
          value: "/api/v1/misc/public"
        - name: STATIC_PRIVATE_ENDPOINT
          value: "/api/v1/misc"
      livenessProbe:
        httpGet:
          path: /
          port: 80
        initialDelaySeconds: 15
        periodSeconds: 30
      dependsOn:
        - api

  restartPolicy: Always
