# 🚀 Refly Pod 部署指南

本指南介绍如何使用 Podman Pod 方式部署 Refly 项目，实现一键启动和统一管理。

## 🌟 Pod 部署的优势

- **统一管理**: 所有服务在一个 Pod 中，一键启动/停止
- **共享网络**: 容器间通过 localhost 通信，配置简单
- **资源隔离**: 更好的资源控制和安全隔离
- **简化运维**: 减少网络配置复杂度

## 📋 系统要求

- **CPU**: ≥ 2 核
- **内存**: ≥ 4GB
- **存储**: ≥ 10GB 可用空间
- **Podman**: 版本 3.0+

## 🚀 快速开始

### 1. 一键部署

```bash
cd deploy/docker
./deploy-pod.sh
```

### 2. 快速管理

```bash
# 交互式管理界面
./refly-pod-quick.sh

# 或使用命令行
./refly-pod-quick.sh start    # 启动
./refly-pod-quick.sh stop     # 停止
./refly-pod-quick.sh restart  # 重启
./refly-pod-quick.sh status   # 状态
```

## 🌐 访问地址

部署成功后，可通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| **Web 界面** | http://localhost:15700 | 主要访问入口 |
| **API 服务** | http://localhost:15800 | API 接口 |
| **MinIO 控制台** | http://localhost:19001 | 对象存储管理 (minioadmin/minioadmin) |
| **Redis 管理** | http://localhost:18001 | Redis 数据管理 |
| **SearXNG** | http://localhost:18080 | 搜索引擎 |

## 📁 文件说明

| 文件 | 说明 |
|------|------|
| `deploy-pod.sh` | 一键部署脚本 |
| `manage-pod.sh` | 完整管理脚本 |
| `refly-pod-quick.sh` | 快速管理脚本 |
| `refly-pod.yml` | Pod 配置文件 (Kubernetes 格式) |

## 🛠️ 管理命令

### 基础操作

```bash
# 查看 Pod 状态
podman pod ps

# 查看容器状态
podman ps --filter pod=refly-pod

# 启动 Pod
podman pod start refly-pod

# 停止 Pod
podman pod stop refly-pod

# 删除 Pod (会删除所有容器)
podman pod rm refly-pod
```

### 使用管理脚本

```bash
# 完整管理功能
./manage-pod.sh status          # 查看状态
./manage-pod.sh logs api        # 查看 API 日志
./manage-pod.sh shell db        # 进入数据库
./manage-pod.sh backup          # 备份数据
./manage-pod.sh update          # 更新镜像

# 快速管理
./refly-pod-quick.sh           # 交互式界面
./refly-pod-quick.sh start     # 启动
./refly-pod-quick.sh open      # 打开浏览器
```

## 🏗️ 架构说明

### Pod 结构

```
refly-pod
├── refly-db        (PostgreSQL 数据库)
├── refly-redis     (Redis 缓存)
├── refly-minio     (MinIO 对象存储)
├── refly-qdrant    (Qdrant 向量数据库)
├── refly-searxng   (SearXNG 搜索引擎)
├── refly-api       (Refly API 服务)
└── refly-web       (Refly Web 前端)
```

### 网络配置

- **Pod 内部**: 所有容器共享网络，通过 localhost 通信
- **外部访问**: 通过端口映射访问服务
- **端口范围**: 15xxx 和 1xxxx 系列，避免与常用端口冲突

### 数据持久化

| 卷名 | 挂载点 | 说明 |
|------|--------|------|
| `refly-db-data` | `/var/lib/postgresql/data` | 数据库数据 |
| `refly-minio-data` | `/data` | 对象存储数据 |
| `refly-redis-data` | `/data` | Redis 数据 |
| `refly-qdrant-data` | `/qdrant/storage` | 向量数据 |

## 🔧 配置说明

### 环境变量

Pod 内服务使用 localhost 进行通信：

```bash
DATABASE_URL=postgresql://refly:test@localhost:5432/refly?schema=refly
REDIS_HOST=localhost
MINIO_INTERNAL_ENDPOINT=localhost
QDRANT_HOST=localhost
SEARXNG_BASE_URL=http://localhost:8080
```

### 端口映射

| 内部端口 | 外部端口 | 服务 |
|----------|----------|------|
| 80 | 15700 | Web 前端 |
| 5800 | 15800 | API 服务 |
| 5801 | 15801 | WebSocket |
| 5432 | 15432 | PostgreSQL |
| 6379 | 16379 | Redis |
| 8001 | 18001 | Redis UI |
| 9000 | 19000 | MinIO API |
| 9001 | 19001 | MinIO Console |
| 6333 | 16333 | Qdrant |
| 8080 | 18080 | SearXNG |

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :15700
   
   # 修改端口映射
   # 编辑 deploy-pod.sh 中的端口配置
   ```

2. **Pod 启动失败**
   ```bash
   # 查看 Pod 日志
   podman pod logs refly-pod
   
   # 查看特定容器日志
   podman logs refly-api
   ```

3. **服务无法访问**
   ```bash
   # 检查服务状态
   ./manage-pod.sh status
   
   # 重启 Pod
   ./refly-pod-quick.sh restart
   ```

4. **数据丢失**
   ```bash
   # 检查数据卷
   podman volume ls | grep refly
   
   # 恢复备份
   # 参考备份文档
   ```

### 日志查看

```bash
# 查看所有容器日志
./manage-pod.sh logs

# 查看特定服务日志
./manage-pod.sh logs api
./manage-pod.sh logs db
./manage-pod.sh logs web

# 实时跟踪日志
podman logs -f refly-api
```

### 性能监控

```bash
# 查看资源使用
podman stats --filter pod=refly-pod

# 查看 Pod 详细信息
podman pod inspect refly-pod
```

## 🔄 更新升级

```bash
# 使用管理脚本更新
./manage-pod.sh update

# 手动更新
podman pull docker.io/reflyai/refly-api:latest
podman pull docker.io/reflyai/refly-web:latest
./refly-pod-quick.sh restart
```

## 💾 数据备份

```bash
# 自动备份
./manage-pod.sh backup

# 手动备份数据库
podman exec refly-db pg_dump -U refly refly > backup.sql

# 备份 MinIO 数据
podman exec refly-minio tar czf - /data > minio_backup.tar.gz
```

## 🗑️ 完全清理

```bash
# 清理所有数据 (谨慎操作!)
./manage-pod.sh cleanup

# 手动清理
podman pod stop refly-pod
podman pod rm refly-pod
podman volume rm refly-db-data refly-minio-data refly-redis-data refly-qdrant-data
```

## 🎯 最佳实践

1. **定期备份**: 使用 `./manage-pod.sh backup` 定期备份数据
2. **监控日志**: 定期检查服务日志，及时发现问题
3. **资源监控**: 使用 `podman stats` 监控资源使用情况
4. **安全配置**: 修改默认密码和密钥
5. **网络安全**: 在生产环境中配置防火墙规则

## 📞 支持

如果遇到问题：

1. 查看日志：`./manage-pod.sh logs`
2. 检查状态：`./refly-pod-quick.sh status`
3. 重启服务：`./refly-pod-quick.sh restart`
4. 参考官方文档：https://docs.refly.ai/
5. 提交 Issue：https://github.com/refly-ai/refly/issues

---

**部署完成后，请访问 http://localhost:15700 开始使用 Refly！** 🎉
