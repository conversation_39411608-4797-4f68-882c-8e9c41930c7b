#!/bin/bash

# Refly 部署验证脚本
# 验证所有服务是否正常工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证函数
verify_service() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    log_info "验证 $service_name..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        log_success "$service_name 验证通过"
        return 0
    else
        log_error "$service_name 验证失败"
        return 1
    fi
}

# 验证容器状态
verify_containers() {
    log_info "验证容器状态..."
    
    local containers=(
        "refly_db"
        "refly_redis"
        "refly_minio"
        "refly_qdrant"
        "refly_searxng"
        "refly_api"
        "refly_web"
    )
    
    local failed=0
    
    for container in "${containers[@]}"; do
        if podman ps --filter name="$container" --format "{{.Status}}" | grep -q "Up"; then
            log_success "容器 $container 运行正常"
        else
            log_error "容器 $container 未运行"
            failed=$((failed + 1))
        fi
    done
    
    return $failed
}

# 验证网络连接
verify_network() {
    log_info "验证网络连接..."
    
    # 验证 Web 服务
    if verify_service "Web 服务" "http://localhost:5700" "200"; then
        echo "  ✓ Web 界面可访问"
    fi
    
    # 验证 API 服务
    if verify_service "API 服务" "http://localhost:5800" "200"; then
        echo "  ✓ API 服务可访问"
    fi
    
    # 验证 MinIO 控制台
    if verify_service "MinIO 控制台" "http://localhost:39001" "200"; then
        echo "  ✓ MinIO 控制台可访问"
    fi
    
    # 验证 Redis 管理界面
    if verify_service "Redis 管理" "http://localhost:38001" "200"; then
        echo "  ✓ Redis 管理界面可访问"
    fi
    
    # 验证 Qdrant API
    if verify_service "Qdrant API" "http://localhost:36333" "200"; then
        echo "  ✓ Qdrant API 可访问"
    fi
    
    # 验证 SearXNG
    if verify_service "SearXNG" "http://localhost:38080" "200"; then
        echo "  ✓ SearXNG 搜索引擎可访问"
    fi
}

# 验证数据库连接
verify_database() {
    log_info "验证数据库连接..."
    
    if podman exec refly_db pg_isready -U refly >/dev/null 2>&1; then
        log_success "PostgreSQL 数据库连接正常"
        
        # 检查数据库表
        local table_count=$(podman exec refly_db psql -U refly -d refly -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'refly';" 2>/dev/null | tr -d ' ')
        if [ "$table_count" -gt 0 ]; then
            log_success "数据库表已创建 ($table_count 个表)"
        else
            log_warning "数据库表尚未创建，可能需要等待 API 服务完全启动"
        fi
    else
        log_error "PostgreSQL 数据库连接失败"
        return 1
    fi
}

# 验证 Redis 连接
verify_redis() {
    log_info "验证 Redis 连接..."
    
    if podman exec refly_redis redis-cli ping >/dev/null 2>&1; then
        log_success "Redis 连接正常"
    else
        log_error "Redis 连接失败"
        return 1
    fi
}

# 验证 MinIO 连接
verify_minio() {
    log_info "验证 MinIO 连接..."
    
    if curl -s http://localhost:39000/minio/health/live >/dev/null 2>&1; then
        log_success "MinIO 服务正常"
    else
        log_error "MinIO 服务异常"
        return 1
    fi
}

# 验证 Qdrant 连接
verify_qdrant() {
    log_info "验证 Qdrant 连接..."
    
    if curl -s http://localhost:36333/healthz | grep -q "healthz check passed"; then
        log_success "Qdrant 向量数据库正常"
    else
        log_error "Qdrant 向量数据库异常"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    log_success "🎉 Refly 部署验证完成！"
    echo ""
    echo "📱 访问地址："
    echo "  🌐 Web 界面:      http://localhost:5700"
    echo "  🔧 API 服务:      http://localhost:5800"
    echo "  📦 MinIO 控制台:  http://localhost:39001"
    echo "     用户名/密码:   minioadmin/minioadmin"
    echo "  🗄️  Redis 管理:    http://localhost:38001"
    echo "  🔍 Qdrant API:    http://localhost:36333"
    echo "  🔎 SearXNG:       http://localhost:38080"
    echo ""
    echo "🛠️  管理命令："
    echo "  查看状态:         ./manage-refly.sh status"
    echo "  查看日志:         ./manage-refly.sh logs"
    echo "  重启服务:         ./manage-refly.sh restart"
    echo "  停止服务:         ./manage-refly.sh stop"
    echo ""
    echo "📚 下一步："
    echo "  1. 访问 Web 界面注册账户"
    echo "  2. 在设置中配置大模型 API"
    echo "  3. 开始使用 Refly！"
}

# 主函数
main() {
    echo "🚀 开始验证 Refly 部署..."
    echo ""
    
    local failed=0
    
    # 验证容器状态
    if ! verify_containers; then
        failed=$((failed + 1))
    fi
    
    echo ""
    
    # 验证网络连接
    verify_network
    
    echo ""
    
    # 验证数据库
    if ! verify_database; then
        failed=$((failed + 1))
    fi
    
    # 验证 Redis
    if ! verify_redis; then
        failed=$((failed + 1))
    fi
    
    # 验证 MinIO
    if ! verify_minio; then
        failed=$((failed + 1))
    fi
    
    # 验证 Qdrant
    if ! verify_qdrant; then
        failed=$((failed + 1))
    fi
    
    echo ""
    
    if [ $failed -eq 0 ]; then
        show_access_info
        exit 0
    else
        log_error "验证过程中发现 $failed 个问题，请检查日志"
        echo ""
        echo "🔧 故障排除："
        echo "  查看日志:         ./manage-refly.sh logs"
        echo "  查看特定服务:     ./manage-refly.sh logs <service>"
        echo "  重启服务:         ./manage-refly.sh restart"
        exit 1
    fi
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
