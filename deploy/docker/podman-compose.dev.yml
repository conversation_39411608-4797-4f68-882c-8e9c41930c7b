include:
  - path: podman-compose.middleware.yml

services:
  api:
    build:
      context: ../../
      dockerfile: apps/api/Dockerfile
    container_name: refly_api
    depends_on:
      db:
        condition: service_healthy
      minio:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    # Podman 使用不同的主机网络访问方式
    ports:
      - "5800:5800"
      - "5801:5801"
    restart: always
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************/refly
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_USE_SSL=false
      - QDRANT_URL=http://qdrant:6333
      - SEARXNG_BASE_URL=http://searxng:8080
      - AUTH_SKIP_VERIFICATION=true
      - JWT_SECRET=your-secret-key-here
      - ENCRYPTION_KEY=your-32-char-encryption-key-here
    networks:
      - refly_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5800"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s

  web:
    image: docker.io/reflyai/refly-web:latest
    container_name: refly_web
    depends_on:
      api:
        condition: service_healthy
    ports:
      - "5700:80"
    restart: always
    networks:
      - refly_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s

networks:
  refly_network:
    driver: bridge
