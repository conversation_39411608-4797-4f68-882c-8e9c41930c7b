#!/bin/bash

# Refly 服务管理脚本
# 用于管理 Podman 部署的 Refly 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 compose 命令
check_compose_cmd() {
    if command -v podman-compose &> /dev/null; then
        COMPOSE_CMD="podman-compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        log_error "未找到 podman-compose 或 docker-compose"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动 Refly 服务..."
    $COMPOSE_CMD -f podman-compose.yml up -d
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止 Refly 服务..."
    $COMPOSE_CMD -f podman-compose.yml down
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    log_info "重启 Refly 服务..."
    $COMPOSE_CMD -f podman-compose.yml restart
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    log_info "Refly 服务状态："
    echo ""
    podman ps --filter name=refly_ --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    
    # 检查服务健康状态
    log_info "服务健康检查："
    
    # 检查 Web 服务
    if curl -f http://localhost:5700 >/dev/null 2>&1; then
        log_success "Web 服务 (http://localhost:5700) - 正常"
    else
        log_error "Web 服务 (http://localhost:5700) - 异常"
    fi
    
    # 检查 API 服务
    if curl -f http://localhost:5800 >/dev/null 2>&1; then
        log_success "API 服务 (http://localhost:5800) - 正常"
    else
        log_error "API 服务 (http://localhost:5800) - 异常"
    fi
    
    # 检查数据库
    if podman exec refly_db pg_isready -U refly >/dev/null 2>&1; then
        log_success "PostgreSQL 数据库 - 正常"
    else
        log_error "PostgreSQL 数据库 - 异常"
    fi
    
    # 检查 Redis
    if podman exec refly_redis redis-cli ping >/dev/null 2>&1; then
        log_success "Redis 缓存 - 正常"
    else
        log_error "Redis 缓存 - 异常"
    fi
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        log_info "显示所有服务日志："
        $COMPOSE_CMD -f podman-compose.yml logs -f
    else
        log_info "显示 $service 服务日志："
        podman logs -f "refly_$service"
    fi
}

# 清理资源
cleanup() {
    log_warning "这将删除所有 Refly 相关的容器、卷和网络"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "停止并删除服务..."
        $COMPOSE_CMD -f podman-compose.yml down -v
        
        log_info "删除网络..."
        podman network rm refly_network 2>/dev/null || true
        
        log_info "删除未使用的卷..."
        podman volume prune -f
        
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 更新服务
update_services() {
    log_info "更新 Refly 服务..."
    
    # 拉取最新镜像
    log_info "拉取最新镜像..."
    podman pull docker.io/reflyai/refly-api:latest
    podman pull docker.io/reflyai/refly-web:latest
    
    # 重新创建容器
    log_info "重新创建容器..."
    $COMPOSE_CMD -f podman-compose.yml up -d --force-recreate
    
    log_success "服务更新完成"
}

# 备份数据
backup_data() {
    local backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    log_info "备份数据到: $backup_dir"
    
    # 备份数据库
    log_info "备份 PostgreSQL 数据库..."
    podman exec refly_db pg_dump -U refly refly > "$backup_dir/database.sql"
    
    # 备份 MinIO 数据
    log_info "备份 MinIO 数据..."
    podman exec refly_minio tar czf - /data > "$backup_dir/minio_data.tar.gz"
    
    log_success "数据备份完成: $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "Refly 服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    显示服务状态"
    echo "  logs      显示所有服务日志"
    echo "  logs <service>  显示指定服务日志 (api, web, db, redis, minio, qdrant, searxng)"
    echo "  update    更新服务到最新版本"
    echo "  backup    备份数据"
    echo "  fix-network  自动修复网络连接问题"
    echo "  cleanup   清理所有资源（危险操作）"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start          # 启动服务"
    echo "  $0 logs api       # 查看 API 服务日志"
    echo "  $0 status         # 查看服务状态"
}

# 主函数
main() {
    check_compose_cmd
    
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        update)
            update_services
            ;;
        backup)
            backup_data
            ;;
        fix-network)
            if [ -f "./fix-network-issues.sh" ]; then
                ./fix-network-issues.sh
            else
                log_error "网络修复脚本不存在"
                exit 1
            fi
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
