# Refly Podman 部署指南

本指南将帮助您使用 Podman 替代 Docker 来部署 Refly 项目。

## 系统要求

- **CPU**: ≥ 2 核
- **内存**: ≥ 4GB
- **存储**: ≥ 10GB 可用空间
- **操作系统**: Linux/macOS/Windows
- **Podman**: 版本 3.0+

## 快速开始

### 1. 安装 Podman

#### CentOS/RHEL/Fedora
```bash
sudo dnf install podman podman-compose
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install podman
pip install podman-compose
```

#### macOS
```bash
brew install podman
pip install podman-compose

# 初始化 Podman 机器
podman machine init
podman machine start
```

### 2. 一键部署

```bash
# 进入部署目录
cd deploy/docker

# 运行部署脚本
./deploy-with-podman.sh
```

### 3. 手动部署步骤

如果您希望手动控制部署过程：

```bash
# 1. 设置镜像源（可选，加速镜像拉取）
./setup-podman-mirrors.sh

# 2. 检查配置文件
ls -la .env podman-compose.yml

# 3. 拉取镜像
podman pull docker.io/reflyai/refly-api:latest
podman pull docker.io/reflyai/refly-web:latest
# ... 其他镜像

# 4. 创建网络
podman network create refly_network

# 5. 启动服务
podman-compose -f podman-compose.yml up -d
```

## 服务管理

使用提供的管理脚本来管理服务：

```bash
# 查看服务状态
./manage-refly.sh status

# 启动服务
./manage-refly.sh start

# 停止服务
./manage-refly.sh stop

# 重启服务
./manage-refly.sh restart

# 查看日志
./manage-refly.sh logs
./manage-refly.sh logs api  # 查看特定服务日志

# 更新服务
./manage-refly.sh update

# 备份数据
./manage-refly.sh backup
```

## 访问地址

部署成功后，您可以通过以下地址访问服务：

- **Web 界面**: http://localhost:5700
- **API 服务**: http://localhost:5800
- **MinIO 控制台**: http://localhost:39001 (用户名/密码: minioadmin/minioadmin)
- **Redis 管理**: http://localhost:38001

## 配置说明

### 环境变量配置

主要的环境变量配置在 `.env` 文件中：

```bash
# 数据库配置
DATABASE_URL=*******************************/refly?schema=refly

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379

# MinIO 配置
MINIO_INTERNAL_ENDPOINT=minio
MINIO_INTERNAL_ACCESS_KEY=minioadmin
MINIO_INTERNAL_SECRET_KEY=minioadmin

# 向量数据库配置
QDRANT_HOST=qdrant
QDRANT_PORT=6333

# JWT 密钥（生产环境请修改）
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 加密密钥（用于 API 密钥加密）
ENCRYPTION_KEY=0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
```

### 端口映射

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|----------|----------|------|
| Web | 80 | 5700 | Web 界面 |
| API | 5800 | 5800 | API 服务 |
| API WebSocket | 5801 | 5801 | WebSocket 连接 |
| PostgreSQL | 5432 | 35432 | 数据库 |
| Redis | 6379 | 36379 | 缓存 |
| MinIO API | 9000 | 39000 | 对象存储 API |
| MinIO Console | 9001 | 39001 | MinIO 管理界面 |
| Qdrant | 6333 | 36333 | 向量数据库 |
| SearXNG | 8080 | 38080 | 搜索引擎 |

## 故障排除

### 常见问题

1. **镜像拉取失败**
   ```bash
   # 检查网络连接
   podman pull docker.io/library/hello-world
   
   # 使用镜像源
   ./setup-podman-mirrors.sh
   ```

2. **服务启动失败**
   ```bash
   # 查看服务日志
   ./manage-refly.sh logs
   
   # 查看特定服务日志
   podman logs refly_api
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库状态
   podman exec refly_db pg_isready -U refly
   
   # 进入数据库
   podman exec -it refly_db psql -U refly -d refly
   ```

4. **权限问题**
   ```bash
   # 使用无根模式
   podman-compose --podman-run-args="--userns=keep-id" up -d
   ```

### 健康检查

```bash
# 检查所有容器状态
podman ps --filter name=refly_

# 检查网络连接
curl http://localhost:5700
curl http://localhost:5800

# 检查数据库
podman exec refly_db pg_isready -U refly

# 检查 Redis
podman exec refly_redis redis-cli ping
```

## 数据持久化

数据存储在以下 Podman 卷中：

- `db_data`: PostgreSQL 数据
- `minio_data`: MinIO 对象存储数据
- `redis_data`: Redis 数据
- `qdrant_data`: Qdrant 向量数据

### 备份数据

```bash
# 使用管理脚本备份
./manage-refly.sh backup

# 手动备份数据库
podman exec refly_db pg_dump -U refly refly > backup.sql

# 手动备份 MinIO 数据
podman exec refly_minio tar czf - /data > minio_backup.tar.gz
```

## 性能优化

### 资源限制

在生产环境中，建议为容器设置资源限制：

```yaml
# 在 podman-compose.yml 中添加
services:
  api:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### 网络优化

```bash
# 创建自定义网络
podman network create --driver bridge refly_network
```

## 安全建议

1. **修改默认密码**
   - 修改 `.env` 文件中的 `JWT_SECRET`
   - 修改 MinIO 的默认用户名密码
   - 生成新的 `ENCRYPTION_KEY`

2. **网络安全**
   - 在生产环境中，不要暴露所有端口到公网
   - 使用反向代理（如 Nginx）
   - 启用 HTTPS

3. **数据安全**
   - 定期备份数据
   - 使用强密码
   - 限制数据库访问权限

## 更新升级

```bash
# 更新到最新版本
./manage-refly.sh update

# 手动更新
podman pull docker.io/reflyai/refly-api:latest
podman pull docker.io/reflyai/refly-web:latest
podman-compose -f podman-compose.yml up -d --force-recreate
```

## 卸载

```bash
# 完全清理所有资源
./manage-refly.sh cleanup
```

## 支持

如果遇到问题，请：

1. 查看日志：`./manage-refly.sh logs`
2. 检查服务状态：`./manage-refly.sh status`
3. 参考官方文档：https://docs.refly.ai/
4. 提交 Issue：https://github.com/refly-ai/refly/issues
