#!/bin/bash

# Refly Pod 管理脚本
# 用于管理 Podman Pod 部署的 Refly 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Pod 和容器名称
POD_NAME="refly-pod"
CONTAINERS=(
    "refly-db"
    "refly-redis" 
    "refly-minio"
    "refly-qdrant"
    "refly-searxng"
    "refly-api"
    "refly-web"
)

# 显示使用帮助
show_help() {
    echo "Refly Pod 管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  start       启动 Pod"
    echo "  stop        停止 Pod"
    echo "  restart     重启 Pod"
    echo "  status      查看 Pod 状态"
    echo "  logs        查看日志 [容器名]"
    echo "  shell       进入容器 <容器名>"
    echo "  update      更新镜像并重启"
    echo "  backup      备份数据"
    echo "  cleanup     完全清理 Pod 和数据"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                    # 启动 Pod"
    echo "  $0 logs api                 # 查看 API 服务日志"
    echo "  $0 shell refly-db           # 进入数据库容器"
    echo "  $0 backup                   # 备份数据"
}

# 启动 Pod
start_pod() {
    log_info "启动 Refly Pod..."
    
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        podman pod start "$POD_NAME"
        log_success "Pod 启动成功"
    else
        log_error "Pod 不存在，请先运行部署脚本: ./deploy-pod.sh"
        exit 1
    fi
}

# 停止 Pod
stop_pod() {
    log_info "停止 Refly Pod..."
    
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        podman pod stop "$POD_NAME"
        log_success "Pod 停止成功"
    else
        log_warning "Pod 不存在"
    fi
}

# 重启 Pod
restart_pod() {
    log_info "重启 Refly Pod..."
    stop_pod
    sleep 3
    start_pod
}

# 查看状态
show_status() {
    log_info "Refly Pod 状态："
    echo ""
    
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        echo "📦 Pod 状态："
        podman pod ps --filter name="$POD_NAME"
        echo ""
        
        echo "🐳 容器状态："
        podman ps --filter pod="$POD_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        
        echo "🌐 服务访问地址："
        echo "  Web 界面: http://localhost:15700"
        echo "  API 服务: http://localhost:15800"
        echo "  MinIO 控制台: http://localhost:19001"
        echo "  Redis 管理: http://localhost:18001"
        echo "  SearXNG: http://localhost:18080"
        echo ""
        
        # 检查服务健康状态
        echo "🔍 服务健康检查："
        check_service_health "Web" "http://localhost:15700"
        check_service_health "API" "http://localhost:15800"
        check_service_health "MinIO" "http://localhost:19001"
        check_service_health "Redis UI" "http://localhost:18001"
    else
        log_warning "Pod 不存在，请先运行部署脚本: ./deploy-pod.sh"
    fi
}

# 检查服务健康状态
check_service_health() {
    local service_name="$1"
    local url="$2"
    
    if curl -f "$url" >/dev/null 2>&1; then
        echo "  ✅ $service_name: 正常"
    else
        echo "  ❌ $service_name: 异常"
    fi
}

# 查看日志
show_logs() {
    local container_name="$1"
    
    if [ -z "$container_name" ]; then
        log_info "显示所有容器日志..."
        for container in "${CONTAINERS[@]}"; do
            if podman container exists "$container" 2>/dev/null; then
                echo ""
                log_info "=== $container 日志 ==="
                podman logs --tail 10 "$container"
            fi
        done
    else
        # 添加前缀如果没有
        if [[ ! "$container_name" =~ ^refly- ]]; then
            container_name="refly-$container_name"
        fi
        
        if podman container exists "$container_name" 2>/dev/null; then
            log_info "显示 $container_name 日志..."
            podman logs -f "$container_name"
        else
            log_error "容器 $container_name 不存在"
            echo "可用容器："
            for container in "${CONTAINERS[@]}"; do
                echo "  - $container"
            done
        fi
    fi
}

# 进入容器
enter_shell() {
    local container_name="$1"
    
    if [ -z "$container_name" ]; then
        log_error "请指定容器名称"
        echo "可用容器："
        for container in "${CONTAINERS[@]}"; do
            echo "  - $container"
        done
        exit 1
    fi
    
    # 添加前缀如果没有
    if [[ ! "$container_name" =~ ^refly- ]]; then
        container_name="refly-$container_name"
    fi
    
    if podman container exists "$container_name" 2>/dev/null; then
        log_info "进入容器 $container_name..."
        
        # 根据容器类型选择合适的 shell
        case "$container_name" in
            *db*)
                podman exec -it "$container_name" psql -U refly -d refly
                ;;
            *redis*)
                podman exec -it "$container_name" redis-cli
                ;;
            *)
                podman exec -it "$container_name" /bin/sh
                ;;
        esac
    else
        log_error "容器 $container_name 不存在或未运行"
    fi
}

# 更新镜像
update_images() {
    log_info "更新 Refly 镜像..."
    
    images=(
        "docker.io/reflyai/refly-api:latest"
        "docker.io/reflyai/refly-web:latest"
        "docker.io/library/postgres:16-alpine"
        "docker.io/minio/minio:RELEASE.2025-01-20T14-49-07Z"
        "docker.io/redis/redis-stack:latest"
        "docker.io/reflyai/qdrant:v1.13.1"
        "docker.io/searxng/searxng:latest"
    )
    
    for image in "${images[@]}"; do
        log_info "更新镜像: $image"
        podman pull "$image"
    done
    
    log_success "镜像更新完成"
    log_info "重启 Pod 以应用更新..."
    restart_pod
}

# 备份数据
backup_data() {
    local backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "备份 Refly 数据到: $backup_dir"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    if podman container exists "refly-db" 2>/dev/null; then
        log_info "备份 PostgreSQL 数据库..."
        podman exec refly-db pg_dump -U refly refly > "$backup_dir/database.sql"
        log_success "数据库备份完成"
    fi
    
    # 备份 MinIO 数据
    if podman container exists "refly-minio" 2>/dev/null; then
        log_info "备份 MinIO 数据..."
        podman exec refly-minio tar czf - /data > "$backup_dir/minio_data.tar.gz"
        log_success "MinIO 数据备份完成"
    fi
    
    # 备份卷信息
    log_info "备份卷信息..."
    podman volume ls > "$backup_dir/volumes.txt"
    
    log_success "备份完成: $backup_dir"
}

# 完全清理
cleanup_all() {
    log_warning "⚠️  这将删除所有 Refly Pod 数据，包括数据库、文件等！"
    read -p "确定要继续吗？(输入 'yes' 确认): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log_info "操作已取消"
        exit 0
    fi
    
    log_info "停止并删除 Pod..."
    stop_pod
    
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        podman pod rm "$POD_NAME"
    fi
    
    log_info "删除数据卷..."
    volumes=(
        "refly-db-data"
        "refly-minio-data"
        "refly-redis-data"
        "refly-qdrant-data"
    )
    
    for volume in "${volumes[@]}"; do
        if podman volume exists "$volume" 2>/dev/null; then
            podman volume rm "$volume"
            log_info "删除卷: $volume"
        fi
    done
    
    log_success "清理完成"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_pod
            ;;
        stop)
            stop_pod
            ;;
        restart)
            restart_pod
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        shell)
            enter_shell "$2"
            ;;
        update)
            update_images
            ;;
        backup)
            backup_data
            ;;
        cleanup)
            cleanup_all
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
