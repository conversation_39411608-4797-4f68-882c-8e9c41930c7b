# 🚀 Google Gemini 配置指南

## 问题解决状态

✅ **已修复**：Google Gemini 提供商认证问题  
✅ **网络连接**：容器可正常访问 Google API  
✅ **代码修复**：已应用 `x-goog-api-key` 认证支持  

## 🔧 修复内容

### 1. 认证方式修复
- **问题**：Gemini API 使用 `x-goog-api-key` 头部认证，而不是标准的 `Authorization: Bearer`
- **修复**：在 `provider-checker.ts` 中添加了 Gemini 专用认证逻辑
- **效果**：现在系统能正确识别并使用 Gemini API 的认证方式

### 2. 网络连接验证
- **测试结果**：容器可以正常访问 `generativelanguage.googleapis.com`
- **API 响应**：端点返回预期的认证错误（说明连接正常）

## 📋 配置步骤

### 第一步：获取 Gemini API Key

1. **访问 Google AI Studio**：
   ```
   https://ai.google.dev/gemini-api/docs/api-key
   ```

2. **创建 API Key**：
   - 登录 Google 账号
   - 点击 "Get API Key"
   - 创建新的 API Key
   - 复制并保存 API Key

### 第二步：在 Refly 中配置 Gemini

1. **访问 Web 界面**：http://localhost:5700

2. **进入提供商设置**：
   - 登录后进入设置页面
   - 选择"提供商"或"Providers"

3. **添加 Google Gemini 提供商**：
   ```
   提供商名称: Google Gemini
   提供商类型: 选择 "Google Gemini"
   Base URL: https://generativelanguage.googleapis.com/v1beta
   API Key: [您的 Gemini API Key]
   ```

4. **测试连接**：
   - 点击"测试连接"或"安装"按钮
   - 应该显示连接成功

### 第三步：配置模型

1. **添加 LLM 模型**：
   ```
   模型 ID: gemini-2.0-flash-exp
   模型名称: Gemini 2.0 Flash
   上下文限制: 1000000
   最大输出 Tokens: 8192
   ```

2. **其他推荐模型**：
   ```
   - gemini-1.5-pro: 上下文 2M，输出 8K
   - gemini-1.5-flash: 上下文 1M，输出 8K
   - gemini-2.0-flash-exp: 上下文 1M，输出 8K（实验版）
   ```

## 🧪 验证配置

### 测试脚本
运行以下命令验证配置：
```bash
./test-gemini-fix.sh
```

### 手动验证
1. **网络连接测试**：
   ```bash
   podman exec refly_api curl -H "x-goog-api-key: YOUR_API_KEY" \
     "https://generativelanguage.googleapis.com/v1beta/models"
   ```

2. **在 Web 界面测试**：
   - 创建新对话
   - 选择 Gemini 模型
   - 发送测试消息

## 🔍 故障排除

### 常见问题

1. **"安装 Google Gemini 失败"**
   - ✅ 已修复：认证问题已解决
   - 确保 API Key 正确
   - 检查网络连接

2. **API Key 无效**
   ```
   错误：API key not valid
   解决：检查 API Key 是否正确复制，是否有权限
   ```

3. **网络连接问题**
   ```bash
   # 测试网络连接
   podman exec refly_api curl -I https://generativelanguage.googleapis.com
   ```

### 日志检查
```bash
# 查看 API 服务日志
./manage-refly.sh logs api

# 查看最近错误
podman logs refly_api --tail 50 | grep -i error
```

## 📊 模型配置参考

### 免费额度模型
| 模型 | 上下文 | 输出 | 特性 |
|------|--------|------|------|
| gemini-1.5-flash | 1M | 8K | 快速响应 |
| gemini-2.0-flash-exp | 1M | 8K | 实验功能 |

### 付费模型
| 模型 | 上下文 | 输出 | 特性 |
|------|--------|------|------|
| gemini-1.5-pro | 2M | 8K | 高质量推理 |
| gemini-2.0-pro-exp | 2M | 8K | 最新功能 |

## 🌐 网络要求

- **地区限制**：Google Gemini 可能在某些地区不可用
- **网络访问**：确保容器可以访问 `generativelanguage.googleapis.com`
- **代理设置**：如需代理，请配置容器网络

## 📝 注意事项

1. **API 配额**：注意 Google 的免费配额限制
2. **模型可用性**：某些实验性模型可能不稳定
3. **数据隐私**：请遵守 Google 的数据使用政策

## 🎯 成功标志

配置成功后，您应该能够：
- ✅ 在提供商列表中看到 Google Gemini
- ✅ 成功测试连接
- ✅ 在对话中使用 Gemini 模型
- ✅ 获得正常的 AI 响应

---

**修复完成时间**：2025-08-18  
**修复方式**：热修复（无需重新构建镜像）  
**影响范围**：Google Gemini 提供商认证  
**状态**：✅ 已解决
