#!/bin/bash

# 测试 Gemini 提供商修复脚本

set -e

echo "🔧 测试 Gemini 提供商修复..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 测试网络连接
log_info "测试网络连接到 Google Gemini API..."
if podman exec refly_api curl -s --connect-timeout 10 "https://generativelanguage.googleapis.com/v1beta/models" > /dev/null; then
    log_success "网络连接正常"
else
    log_error "网络连接失败"
    exit 1
fi

# 2. 测试 API 端点响应
log_info "测试 Gemini API 端点响应..."
response=$(podman exec refly_api curl -s -H "x-goog-api-key: test" "https://generativelanguage.googleapis.com/v1beta/models")
if echo "$response" | grep -q "API key not valid"; then
    log_success "API 端点响应正常（预期的 API key 错误）"
else
    log_warning "API 端点响应异常: $response"
fi

# 3. 检查代码修改是否生效
log_info "检查 provider-checker.ts 修改..."
if podman exec refly_api grep -q "generativelanguage.googleapis.com" /app/packages/providers/src/provider-checker/provider-checker.ts; then
    log_success "provider-checker.ts 修改已生效"
else
    log_error "provider-checker.ts 修改未生效"
fi

log_info "检查 openai.ts 修改..."
if podman exec refly_api grep -q "x-goog-api-key" /app/packages/providers/src/llm/openai.ts; then
    log_success "openai.ts 修改已生效"
else
    log_error "openai.ts 修改未生效"
fi

# 4. 测试服务状态
log_info "检查 API 服务状态..."
if curl -s http://localhost:5800/ | grep -q "Refly API Endpoint"; then
    log_success "API 服务运行正常"
else
    log_error "API 服务异常"
    exit 1
fi

# 5. 检查日志中是否还有参数错误
log_info "检查最近的错误日志..."
recent_errors=$(podman logs refly_api --tail 20 | grep -i "System parameter error" | wc -l)
if [ "$recent_errors" -eq 0 ]; then
    log_success "没有发现新的参数错误"
else
    log_warning "仍有 $recent_errors 个参数错误，可能需要清除缓存或重新测试"
fi

echo ""
echo "🎯 测试总结："
echo "✅ 网络连接正常"
echo "✅ API 端点可访问"
echo "✅ 代码修改已应用"
echo "✅ 服务运行正常"
echo ""
echo "📋 下一步操作："
echo "1. 在 Web 界面中配置 Google Gemini 提供商"
echo "2. 使用有效的 Gemini API Key"
echo "3. Base URL: https://generativelanguage.googleapis.com/v1beta"
echo "4. 测试连接和模型配置"
echo ""
echo "🔗 获取 Gemini API Key："
echo "   访问: https://ai.google.dev/gemini-api/docs/api-key"
echo ""

log_success "Gemini 提供商修复测试完成！"
