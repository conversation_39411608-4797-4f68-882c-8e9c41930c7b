#!/bin/bash

# 自动修复网络连接问题脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 Refly 网络问题自动修复工具"
echo "================================"

# 1. 检测网络问题
log_info "检测网络连接问题..."

# 检查 API 是否可以直接访问
if curl -f -s http://localhost:5800/ >/dev/null 2>&1; then
    log_success "API 服务直接访问正常"
    api_direct_ok=true
else
    log_error "API 服务直接访问失败"
    api_direct_ok=false
fi

# 检查通过 Web 代理的 API 访问
if curl -f -s http://localhost:5700/api/v1/auth/config >/dev/null 2>&1; then
    log_success "Web 代理访问正常"
    web_proxy_ok=true
else
    log_error "Web 代理访问失败"
    web_proxy_ok=false
fi

# 2. 诊断问题类型
if [ "$api_direct_ok" = true ] && [ "$web_proxy_ok" = false ]; then
    log_warning "检测到网络代理问题：API 服务正常，但 Web 代理失败"
    
    # 检查 Web 容器日志中的错误
    log_info "检查 Web 容器日志..."
    recent_502_errors=$(podman logs refly_web --tail 10 | grep -c "502" || echo "0")
    
    if [ "$recent_502_errors" -gt 0 ]; then
        log_error "发现 $recent_502_errors 个 502 错误"
        
        # 检查 DNS 解析
        log_info "检查容器间 DNS 解析..."
        web_dns=$(podman exec refly_web nslookup api | grep "Address:" | tail -1 | awk '{print $2}')
        api_ip=$(podman inspect refly_api | grep '"IPAddress"' | head -1 | cut -d'"' -f4)
        
        log_info "Web 容器解析的 API 地址: $web_dns"
        log_info "API 容器实际地址: $api_ip"
        
        if [ "$web_dns" != "$api_ip" ]; then
            log_error "DNS 解析不匹配，需要重启 Web 容器"
            fix_needed=true
        else
            log_warning "DNS 解析正确，可能是其他网络问题"
            fix_needed=true
        fi
    else
        log_info "未发现明显的 502 错误"
        fix_needed=false
    fi
elif [ "$api_direct_ok" = false ]; then
    log_error "API 服务本身有问题，需要重启 API 容器"
    fix_needed=true
    fix_api=true
else
    log_success "所有服务连接正常"
    fix_needed=false
fi

# 3. 执行修复
if [ "$fix_needed" = true ]; then
    log_info "开始执行自动修复..."
    
    if [ "$fix_api" = true ]; then
        log_info "重启 API 容器..."
        podman restart refly_api
        sleep 10
    fi
    
    log_info "重启 Web 容器以刷新网络配置..."
    podman restart refly_web
    
    log_info "等待服务启动..."
    sleep 10
    
    # 4. 验证修复效果
    log_info "验证修复效果..."
    
    retry_count=0
    max_retries=6
    
    while [ $retry_count -lt $max_retries ]; do
        if curl -f -s http://localhost:5700/api/v1/auth/config >/dev/null 2>&1; then
            log_success "✅ 网络连接已修复！"
            
            # 运行完整验证
            log_info "运行完整服务验证..."
            if ./verify-deployment.sh >/dev/null 2>&1; then
                log_success "✅ 所有服务验证通过"
            else
                log_warning "⚠️  部分服务可能仍有问题，请检查详细日志"
            fi
            
            echo ""
            echo "🎉 修复完成！"
            echo "📱 现在可以刷新浏览器页面: http://localhost:5700"
            exit 0
        else
            retry_count=$((retry_count + 1))
            log_info "等待服务启动... ($retry_count/$max_retries)"
            sleep 5
        fi
    done
    
    log_error "❌ 自动修复失败，请手动检查服务状态"
    echo ""
    echo "🔍 手动排查步骤："
    echo "1. 检查服务状态: ./manage-refly.sh status"
    echo "2. 查看日志: ./manage-refly.sh logs"
    echo "3. 重启所有服务: ./manage-refly.sh restart"
    exit 1
else
    log_success "✅ 无需修复，所有服务正常运行"
    echo ""
    echo "📱 可以正常使用: http://localhost:5700"
fi

echo ""
echo "💡 预防措施："
echo "- 如果经常遇到此问题，可以考虑使用固定 IP 配置"
echo "- 定期运行健康检查: ./verify-deployment.sh"
echo "- 监控服务日志: ./manage-refly.sh logs"
