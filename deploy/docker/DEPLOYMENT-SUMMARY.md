# 🎉 Refly Podman 部署成功！

## 部署状态

✅ **部署完成** - 所有服务正常运行  
✅ **验证通过** - 所有组件健康检查通过  
✅ **数据库就绪** - PostgreSQL 已创建 42 个数据表  

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **Web 界面** | http://localhost:5700 | 主要使用界面 |
| **API 服务** | http://localhost:5800 | REST API 接口 |
| **MinIO 控制台** | http://localhost:39001 | 对象存储管理 (minioadmin/minioadmin) |
| **Redis 管理** | http://localhost:38001 | 缓存数据管理 |
| **Qdrant API** | http://localhost:36333 | 向量数据库 API |
| **SearXNG** | http://localhost:38080 | 搜索引擎服务 |

## 🐳 容器状态

| 容器名 | 状态 | 端口映射 |
|--------|------|----------|
| refly_web | ✅ 健康 | 5700:80 |
| refly_api | ✅ 健康 | 5800:5800, 5801:5801 |
| refly_db | ✅ 健康 | 35432:5432 |
| refly_redis | ✅ 健康 | 36379:6379, 38001:8001 |
| refly_minio | ✅ 健康 | 39000:9000, 39001:9001 |
| refly_qdrant | ✅ 健康 | 36333:6333, 36334:6334 |
| refly_searxng | ✅ 运行 | 38080:8080 |

## 🛠️ 管理命令

```bash
# 查看服务状态
./manage-refly.sh status

# 查看所有服务日志
./manage-refly.sh logs

# 查看特定服务日志
./manage-refly.sh logs api
./manage-refly.sh logs web

# 重启服务
./manage-refly.sh restart

# 停止服务
./manage-refly.sh stop

# 启动服务
./manage-refly.sh start

# 更新服务
./manage-refly.sh update

# 备份数据
./manage-refly.sh backup

# 验证部署
./verify-deployment.sh
```

## 📁 重要文件

| 文件 | 说明 |
|------|------|
| `podman-compose.yml` | 主服务配置文件 |
| `podman-compose.middleware.yml` | 中间件服务配置 |
| `.env` | 环境变量配置 |
| `deploy-with-podman.sh` | 一键部署脚本 |
| `manage-refly.sh` | 服务管理脚本 |
| `verify-deployment.sh` | 部署验证脚本 |
| `setup-podman-mirrors.sh` | 镜像源配置脚本 |
| `README-PODMAN.md` | 详细部署文档 |

## 🚀 下一步操作

### 1. 访问 Web 界面
打开浏览器访问：http://localhost:5700

### 2. 注册账户
- 点击注册按钮
- 填写邮箱和密码
- 完成账户创建

### 3. 配置大模型 API
进入设置页面，配置您的大模型提供商：

#### OpenAI 兼容 API
```bash
# 在 .env 文件中配置（如果需要全局默认）
OPENAI_BASE_URL=https://your-api-endpoint.com/v1
OPENAI_API_KEY=your-api-key
```

#### 支持的提供商
- OpenAI (GPT-4o, GPT-4o-mini)
- DeepSeek (DeepSeek R1)
- 智谱 AI (GLM 系列)
- Moonshot AI (Kimi)
- SiliconFlow
- 七牛云 AI
- Groq
- NVIDIA
- 火山引擎（豆包）
- 本地 Ollama

### 4. 开始使用
- 创建新的工作空间
- 上传文档到知识库
- 开始与 AI 对话

## 🔧 故障排除

### 常见问题

1. **服务无法访问**
   ```bash
   # 检查服务状态
   ./manage-refly.sh status
   
   # 查看日志
   ./manage-refly.sh logs
   ```

2. **API 服务启动慢**
   - 首次启动需要初始化数据库，请耐心等待
   - 查看 API 日志：`./manage-refly.sh logs api`

3. **内存不足**
   - 确保系统至少有 4GB 可用内存
   - 可以停止不必要的服务释放内存

4. **端口冲突**
   - 检查端口是否被占用：`lsof -i :5700`
   - 修改 `podman-compose.yml` 中的端口映射

### 重新部署

如果需要重新部署：

```bash
# 停止并清理所有资源
./manage-refly.sh cleanup

# 重新部署
./deploy-with-podman.sh
```

## 📊 性能监控

### 资源使用情况
```bash
# 查看容器资源使用
podman stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

### 日志管理
```bash
# 查看日志大小
du -sh ~/.local/share/containers/storage/

# 清理日志（谨慎操作）
podman system prune -f
```

## 🔒 安全建议

1. **修改默认密码**
   - 更改 `.env` 文件中的 `JWT_SECRET`
   - 更改 MinIO 默认密码

2. **网络安全**
   - 生产环境中不要暴露所有端口
   - 使用反向代理（Nginx/Traefik）
   - 启用 HTTPS

3. **数据备份**
   - 定期备份：`./manage-refly.sh backup`
   - 备份重要配置文件

## 📞 获取帮助

- **官方文档**: https://docs.refly.ai/
- **GitHub Issues**: https://github.com/refly-ai/refly/issues
- **Discord 社区**: https://discord.gg/bWjffrb89h
- **部署文档**: `README-PODMAN.md`

---

🎊 **恭喜！您已成功使用 Podman 部署了 Refly 项目！**

现在您可以开始体验这个强大的 AI 工作空间了。如有任何问题，请参考上述故障排除指南或联系社区获取帮助。
