# 🔧 Refly 注册问题故障排除报告

## 问题描述

用户在 Web 界面注册时遇到错误：
```
Oops, something went wrong
An unknown error has occurred. The Refly team is working quickly to resolve it. Please try again later.
```

## 问题分析

### 1. 初步诊断

通过日志分析发现：

**API 服务状态**：✅ 正常
- API 服务本身运行正常
- 直接调用 API 接口注册成功：`{"success":true,"data":{"skipVerification":true}}`

**Web 服务状态**：❌ 代理失败
- Nginx 反向代理出现 502 错误
- 错误信息：`upstream prematurely closed connection while reading response header from upstream`

### 2. 根本原因

**网络连接问题**：
- Web 容器的 Nginx 无法正确连接到 API 容器
- 日志显示尝试连接到错误的 IP 地址：`************:5800`
- 实际 API 容器 IP：`*********`

**网络配置**：
- 容器网络：`docker_refly_network`
- DNS 解析正常：`api.dns.podman` → `*********`
- 容器间通信测试成功

## 解决方案

### 执行的修复步骤

1. **重启 Web 容器**：
   ```bash
   podman restart refly_web
   ```

2. **验证修复效果**：
   ```bash
   # 测试 API 代理
   curl -X POST http://localhost:5700/api/v1/auth/email/signup \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password123"}'
   
   # 预期结果：{"success":true,"data":{"skipVerification":true}}
   ```

3. **验证配置接口**：
   ```bash
   curl -s http://localhost:5700/api/v1/auth/config
   # 预期结果：{"success":true,"data":[{"provider":"email"}]}
   ```

### 修复结果

✅ **问题已解决**
- Web 界面注册功能恢复正常
- API 代理连接正常
- 所有服务健康检查通过

## 预防措施

### 1. 监控脚本增强

在 `verify-deployment.sh` 中添加了更详细的网络连接检查：

```bash
# 验证容器间网络连接
verify_container_network() {
    log_info "验证容器间网络连接..."
    
    if podman exec refly_web wget -qO- --timeout=5 http://api:5800/ >/dev/null 2>&1; then
        log_success "Web → API 网络连接正常"
    else
        log_error "Web → API 网络连接失败"
        return 1
    fi
}
```

### 2. 自动修复脚本

创建网络问题自动修复脚本：

```bash
#!/bin/bash
# fix-network-issues.sh

fix_network_connectivity() {
    log_info "检测到网络连接问题，尝试自动修复..."
    
    # 重启 Web 容器
    podman restart refly_web
    
    # 等待容器启动
    sleep 10
    
    # 验证修复效果
    if curl -f http://localhost:5700/api/v1/auth/config >/dev/null 2>&1; then
        log_success "网络连接问题已修复"
        return 0
    else
        log_error "自动修复失败，需要手动干预"
        return 1
    fi
}
```

### 3. 健康检查改进

在 `podman-compose.yml` 中添加更严格的健康检查：

```yaml
web:
  healthcheck:
    test: ["CMD", "sh", "-c", "curl -f http://localhost:80 && curl -f http://api:5800"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 15s
```

## 常见网络问题排查步骤

### 1. 检查容器状态
```bash
podman ps --filter name=refly_
```

### 2. 检查网络连接
```bash
# 从 Web 容器测试 API 连接
podman exec refly_web wget -qO- --timeout=5 http://api:5800/

# 检查 DNS 解析
podman exec refly_web nslookup api
```

### 3. 检查日志
```bash
# Web 服务日志
./manage-refly.sh logs web

# API 服务日志  
./manage-refly.sh logs api
```

### 4. 重启相关服务
```bash
# 重启 Web 服务
podman restart refly_web

# 或重启所有服务
./manage-refly.sh restart
```

## 经验总结

1. **容器网络问题**通常在容器重启后可以自动解决
2. **502 错误**通常表示反向代理无法连接到上游服务
3. **定期健康检查**可以及早发现网络连接问题
4. **日志分析**是诊断网络问题的关键工具

## 相关文件

- `podman-compose.yml` - 主服务配置
- `manage-refly.sh` - 服务管理脚本
- `verify-deployment.sh` - 部署验证脚本
- `/etc/nginx/conf.d/default.conf` - Nginx 配置（在 Web 容器内）

---

**状态**：✅ 已解决  
**修复时间**：2025-08-18 17:05  
**影响范围**：Web 界面用户注册功能  
**解决方法**：重启 Web 容器刷新网络配置
