#!/bin/bash

# Refly Podman 部署脚本
# 使用 Podman 替代 Docker 部署 Refly 项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Podman 是否安装
check_podman() {
    log_info "检查 Podman 安装状态..."
    if ! command -v podman &> /dev/null; then
        log_error "Podman 未安装，请先安装 Podman"
        exit 1
    fi
    
    PODMAN_VERSION=$(podman --version)
    log_success "Podman 已安装: $PODMAN_VERSION"
}

# 检查 podman-compose 是否安装
check_podman_compose() {
    log_info "检查 podman-compose 安装状态..."
    if command -v podman-compose &> /dev/null; then
        COMPOSE_CMD="podman-compose"
        log_success "使用 podman-compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
        log_warning "使用 docker-compose (将与 Podman 配合使用)"
    else
        log_error "未找到 podman-compose 或 docker-compose，请安装其中之一"
        log_info "安装方法："
        log_info "  pip install podman-compose"
        log_info "  或者安装 docker-compose"
        exit 1
    fi
}

# 设置镜像源
setup_mirrors() {
    log_info "设置 Podman 国内镜像源..."
    if [ -f "./setup-podman-mirrors.sh" ]; then
        ./setup-podman-mirrors.sh
    else
        log_warning "镜像源配置脚本不存在，跳过镜像源设置"
    fi
}

# 预拉取镜像
pull_images() {
    log_info "预拉取所需镜像..."
    
    images=(
        "docker.io/reflyai/refly-api:latest"
        "docker.io/reflyai/refly-web:latest"
        "docker.io/library/postgres:16-alpine"
        "docker.io/minio/minio:RELEASE.2025-01-20T14-49-07Z"
        "docker.io/redis/redis-stack:latest"
        "docker.io/reflyai/qdrant:v1.13.1"
        "docker.io/searxng/searxng:latest"
    )
    
    for image in "${images[@]}"; do
        log_info "拉取镜像: $image"
        if ! podman pull "$image"; then
            log_error "拉取镜像失败: $image"
            exit 1
        fi
    done
    
    log_success "所有镜像拉取完成"
}

# 创建网络
create_network() {
    log_info "创建 Podman 网络..."
    if ! podman network exists refly_network 2>/dev/null; then
        podman network create refly_network
        log_success "网络 refly_network 创建成功"
    else
        log_info "网络 refly_network 已存在"
    fi
}

# 启动服务
start_services() {
    log_info "启动 Refly 服务..."
    
    # 检查配置文件
    if [ ! -f ".env" ]; then
        log_error ".env 文件不存在，请先配置环境变量"
        exit 1
    fi
    
    if [ ! -f "podman-compose.yml" ]; then
        log_error "podman-compose.yml 文件不存在"
        exit 1
    fi
    
    # 启动服务
    $COMPOSE_CMD -f podman-compose.yml up -d
    
    if [ $? -eq 0 ]; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库
    log_info "等待 PostgreSQL 就绪..."
    for i in {1..30}; do
        if podman exec refly_db pg_isready -U refly >/dev/null 2>&1; then
            log_success "PostgreSQL 已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "PostgreSQL 启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待 API 服务
    log_info "等待 API 服务就绪..."
    for i in {1..60}; do
        if curl -f http://localhost:5800 >/dev/null 2>&1; then
            log_success "API 服务已就绪"
            break
        fi
        if [ $i -eq 60 ]; then
            log_error "API 服务启动超时"
            exit 1
        fi
        sleep 3
    done
    
    # 等待 Web 服务
    log_info "等待 Web 服务就绪..."
    for i in {1..30}; do
        if curl -f http://localhost:5700 >/dev/null 2>&1; then
            log_success "Web 服务已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Web 服务启动超时"
            exit 1
        fi
        sleep 2
    done
}

# 显示服务状态
show_status() {
    log_info "服务状态："
    podman ps --filter name=refly_
    
    echo ""
    log_success "部署完成！"
    log_info "访问地址："
    log_info "  Web 界面: http://localhost:5700"
    log_info "  API 服务: http://localhost:5800"
    log_info "  MinIO 控制台: http://localhost:39001 (minioadmin/minioadmin)"
    log_info "  Redis 管理: http://localhost:38001"
}

# 主函数
main() {
    log_info "开始 Refly Podman 部署..."
    
    check_podman
    check_podman_compose
    setup_mirrors
    pull_images
    create_network
    start_services
    wait_for_services
    show_status
    
    log_success "Refly 部署完成！"
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
