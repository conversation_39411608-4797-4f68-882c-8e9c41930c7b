#!/bin/bash

# 测试 OpenAI 类型的 Gemini 配置
# 验证系统是否正确检测并使用 x-goog-api-key 认证

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 API Key
check_api_key() {
    local api_key="$1"
    
    if [ -z "$api_key" ]; then
        log_error "请提供 Gemini API Key"
        echo "用法: $0 <GEMINI_API_KEY>"
        echo "获取 API Key: https://ai.google.dev/gemini-api/docs/api-key"
        exit 1
    fi
    
    if [[ ! "$api_key" =~ ^AIza ]]; then
        log_warning "API Key 格式可能不正确（应以 'AIza' 开头）"
    fi
    
    log_success "API Key 格式检查通过"
}

# 测试网络连接
test_network() {
    log_info "测试网络连接..."
    
    if curl -s -I https://generativelanguage.googleapis.com/v1beta/models > /dev/null; then
        log_success "网络连接正常"
        return 0
    else
        log_error "无法连接到 Google API"
        return 1
    fi
}

# 测试 API Key 有效性
test_api_key() {
    local api_key="$1"
    
    log_info "测试 API Key 有效性..."
    
    local response=$(curl -s -w "%{http_code}" \
        -H "x-goog-api-key: $api_key" \
        "https://generativelanguage.googleapis.com/v1beta/models" \
        -o /tmp/gemini_response.json)
    
    if [ "$response" = "200" ]; then
        log_success "API Key 有效"
        
        # 显示可用模型
        local model_count=$(jq -r '.models | length' /tmp/gemini_response.json 2>/dev/null || echo "0")
        log_info "可用模型数量: $model_count"
        
        if [ "$model_count" -gt 0 ]; then
            log_info "前3个可用模型:"
            jq -r '.models[0:3][] | "  - \(.name) (\(.displayName))"' /tmp/gemini_response.json 2>/dev/null || echo "  无法解析模型列表"
        fi
        
        return 0
    elif [ "$response" = "401" ]; then
        log_error "API Key 无效或未授权"
        return 1
    elif [ "$response" = "403" ]; then
        log_error "API Key 权限不足或服务未启用"
        return 1
    else
        log_error "API 请求失败，状态码: $response"
        if [ -f /tmp/gemini_response.json ]; then
            log_info "响应内容:"
            cat /tmp/gemini_response.json
        fi
        return 1
    fi
}

# 测试内容生成
test_generation() {
    local api_key="$1"
    
    log_info "测试内容生成..."
    
    local request_body='{
        "contents": [{
            "parts": [{
                "text": "Hello! Please respond with a simple greeting."
            }]
        }]
    }'
    
    local response=$(curl -s -w "%{http_code}" \
        -H "x-goog-api-key: $api_key" \
        -H "Content-Type: application/json" \
        -d "$request_body" \
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent" \
        -o /tmp/gemini_generation.json)
    
    if [ "$response" = "200" ]; then
        log_success "内容生成测试通过"
        
        # 提取生成的文本
        local generated_text=$(jq -r '.candidates[0].content.parts[0].text' /tmp/gemini_generation.json 2>/dev/null || echo "无法解析响应")
        log_info "生成的内容: $generated_text"
        
        return 0
    else
        log_error "内容生成失败，状态码: $response"
        if [ -f /tmp/gemini_generation.json ]; then
            log_info "响应内容:"
            cat /tmp/gemini_generation.json
        fi
        return 1
    fi
}

# 模拟 Refly 的检测逻辑
test_refly_detection() {
    log_info "测试 Refly 检测逻辑..."
    
    # 模拟配置
    local provider_key="openai"
    local base_url="https://generativelanguage.googleapis.com/v1beta"
    
    log_info "模拟配置:"
    log_info "  Provider Key: $provider_key"
    log_info "  Base URL: $base_url"
    
    # 检测逻辑
    if [[ "$base_url" == *"generativelanguage.googleapis.com"* ]]; then
        log_success "✅ 正确检测到 Gemini API"
        log_success "✅ 将使用 x-goog-api-key 认证头"
        return 0
    else
        log_error "❌ 未能检测到 Gemini API"
        log_error "❌ 将使用错误的 Authorization Bearer 认证"
        return 1
    fi
}

# 检查 Refly 服务状态
check_refly_status() {
    log_info "检查 Refly 服务状态..."
    
    if podman pod exists refly-pod 2>/dev/null; then
        local pod_status=$(podman pod ps --filter name=refly-pod --format "{{.Status}}" 2>/dev/null)
        if [[ "$pod_status" == *"Running"* ]]; then
            log_success "Refly Pod 正在运行"
            
            # 检查 API 服务
            if podman ps --filter name=refly-pod-api --format "{{.Status}}" | grep -q "Up"; then
                log_success "API 服务正常运行"
                return 0
            else
                log_warning "API 服务可能未正常运行"
                return 1
            fi
        else
            log_error "Refly Pod 未运行: $pod_status"
            return 1
        fi
    else
        log_error "Refly Pod 不存在"
        return 1
    fi
}

# 生成配置指南
generate_config_guide() {
    local api_key="$1"
    
    log_info "生成配置指南..."
    
    cat << EOF

🎯 Refly 中的 Gemini 配置指南
=====================================

📋 配置参数:
  提供商类型: OpenAI (选择 OpenAI 兼容)
  提供商名称: Google Gemini
  Base URL: https://generativelanguage.googleapis.com/v1beta
  API Key: ${api_key:0:10}...

🔧 配置步骤:
  1. 访问 Web 界面: http://localhost:5700
  2. 进入设置 -> 提供商
  3. 添加新提供商，选择 "OpenAI" 类型
  4. 填入上述配置参数
  5. 点击测试连接

✅ 系统会自动:
  - 检测到这是 Gemini API
  - 使用 x-goog-api-key 认证头
  - 正确处理 API 请求和响应

🤖 推荐模型:
  - gemini-1.5-flash (快速响应)
  - gemini-1.5-pro (高质量推理)
  - gemini-2.0-flash-exp (实验性功能)

EOF
}

# 主函数
main() {
    local api_key="$1"
    
    echo "🧪 Gemini OpenAI 类型配置测试"
    echo "================================"
    echo ""
    
    # 检查参数
    check_api_key "$api_key"
    echo ""
    
    # 运行测试
    local tests_passed=0
    local total_tests=5
    
    # 测试1: 网络连接
    if test_network; then
        ((tests_passed++))
    fi
    echo ""
    
    # 测试2: API Key 有效性
    if test_api_key "$api_key"; then
        ((tests_passed++))
    fi
    echo ""
    
    # 测试3: 内容生成
    if test_generation "$api_key"; then
        ((tests_passed++))
    fi
    echo ""
    
    # 测试4: Refly 检测逻辑
    if test_refly_detection; then
        ((tests_passed++))
    fi
    echo ""
    
    # 测试5: Refly 服务状态
    if check_refly_status; then
        ((tests_passed++))
    fi
    echo ""
    
    # 总结
    echo "📊 测试结果总结"
    echo "==============="
    echo "✅ 通过: $tests_passed/$total_tests"
    echo "❌ 失败: $((total_tests - tests_passed))/$total_tests"
    echo ""
    
    if [ $tests_passed -eq $total_tests ]; then
        log_success "🎉 所有测试通过！可以在 Refly 中配置 Gemini"
        generate_config_guide "$api_key"
    elif [ $tests_passed -ge 3 ]; then
        log_warning "⚠️  大部分测试通过，可以尝试配置"
        generate_config_guide "$api_key"
    else
        log_error "❌ 多个测试失败，请检查网络连接和 API Key"
        echo ""
        echo "🔧 故障排除:"
        echo "  1. 检查网络连接到 Google API"
        echo "  2. 验证 API Key 是否有效"
        echo "  3. 确保启用了 Generative Language API"
        echo "  4. 检查 Google Cloud 项目配额"
    fi
    
    # 清理临时文件
    rm -f /tmp/gemini_response.json /tmp/gemini_generation.json
}

# 运行主函数
main "$@"
