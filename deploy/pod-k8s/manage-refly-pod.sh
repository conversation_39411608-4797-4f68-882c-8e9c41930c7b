#!/bin/bash

# Refly Pod 管理脚本
# 提供启动、停止、重启、状态检查等功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Refly Pod 管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动 Refly Pod"
    echo "  stop      停止 Refly Pod"
    echo "  restart   重启 Refly Pod"
    echo "  status    查看 Pod 状态"
    echo "  logs      查看日志"
    echo "  shell     进入容器 shell"
    echo "  cleanup   清理 Pod 和相关资源"
    echo "  info      显示访问信息"
    echo "  help      显示此帮助信息"
    echo ""
}

# 检查 Pod 是否存在
check_pod_exists() {
    if podman pod exists refly-pod 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 启动 Pod
start_pod() {
    log_info "启动 Refly Pod..."
    
    if check_pod_exists; then
        local status=$(podman pod ps --filter name=refly-pod --format "{{.Status}}" 2>/dev/null)
        if [[ "$status" == *"Running"* ]]; then
            log_warning "Pod 已经在运行中"
            return 0
        fi
        
        podman pod start refly-pod
        log_success "Pod 启动成功"
    else
        log_error "Pod 不存在，请先运行部署脚本"
        return 1
    fi
}

# 停止 Pod
stop_pod() {
    log_info "停止 Refly Pod..."
    
    if check_pod_exists; then
        podman pod stop refly-pod
        log_success "Pod 停止成功"
    else
        log_warning "Pod 不存在"
    fi
}

# 重启 Pod
restart_pod() {
    log_info "重启 Refly Pod..."
    
    if check_pod_exists; then
        podman pod restart refly-pod
        log_success "Pod 重启成功"
    else
        log_error "Pod 不存在，请先运行部署脚本"
        return 1
    fi
}

# 查看状态
show_status() {
    log_info "Refly Pod 状态:"
    echo ""
    
    if check_pod_exists; then
        # Pod 状态
        echo "📦 Pod 状态:"
        podman pod ps --filter name=refly-pod
        echo ""
        
        # 容器状态
        echo "🐳 容器状态:"
        podman ps --filter pod=refly-pod --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        
        # 资源使用情况
        echo "💾 资源使用:"
        podman pod stats refly-pod --no-stream 2>/dev/null || log_warning "无法获取资源使用情况"
        echo ""
    else
        log_warning "Pod 不存在"
    fi
}

# 查看日志
show_logs() {
    local container_name=${2:-""}
    
    if ! check_pod_exists; then
        log_error "Pod 不存在"
        return 1
    fi
    
    if [ -n "$container_name" ]; then
        log_info "查看容器 $container_name 的日志:"
        podman logs -f "refly-pod-$container_name"
    else
        log_info "可用的容器:"
        podman ps --filter pod=refly-pod --format "{{.Names}}" | sed 's/refly-pod-/  - /'
        echo ""
        log_info "使用方法: $0 logs <container_name>"
        echo "例如: $0 logs api"
    fi
}

# 进入容器 shell
enter_shell() {
    local container_name=${2:-"api"}
    
    if ! check_pod_exists; then
        log_error "Pod 不存在"
        return 1
    fi
    
    log_info "进入容器 $container_name 的 shell..."
    podman exec -it "refly-pod-$container_name" /bin/sh
}

# 清理资源
cleanup_pod() {
    log_warning "这将删除 Pod 和所有相关资源，数据将被保留"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理 Refly Pod..."
        
        # 停止并删除 Pod
        if check_pod_exists; then
            podman pod stop refly-pod 2>/dev/null || true
            podman pod rm refly-pod 2>/dev/null || true
        fi
        
        # 清理 Kubernetes 资源
        if [ -f "deploy/pod-k8s/refly-pod-complete.yaml" ]; then
            podman play kube --down deploy/pod-k8s/refly-pod-complete.yaml 2>/dev/null || true
        fi
        
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示访问信息
show_info() {
    log_info "Refly Pod 访问信息:"
    echo ""
    echo "🌐 Web 界面:"
    echo "   http://localhost:5700"
    echo ""
    echo "🔧 API 服务:"
    echo "   HTTP: http://localhost:5800"
    echo "   WebSocket: ws://localhost:5801"
    echo ""
    echo "📊 管理界面:"
    echo "   MinIO 控制台: http://localhost:39001"
    echo "   用户名/密码: minioadmin/minioadmin"
    echo ""
    echo "   Redis Insight: http://localhost:38001"
    echo ""
    echo "🔍 搜索服务:"
    echo "   SearXNG: http://localhost:38080"
    echo ""
    echo "📁 数据存储位置:"
    echo "   /tmp/refly-pod/"
    echo ""
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "start")
            start_pod
            ;;
        "stop")
            stop_pod
            ;;
        "restart")
            restart_pod
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$@"
            ;;
        "shell")
            enter_shell "$@"
            ;;
        "cleanup")
            cleanup_pod
            ;;
        "info")
            show_info
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
