#!/bin/bash

# Refly Pod 部署验证脚本
# 验证所有服务是否正常运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务连接
check_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local path=${4:-"/"}
    local timeout=${5:-10}
    
    log_info "检查 $service_name 服务..."
    
    if command -v curl &> /dev/null; then
        if curl -f -s --max-time $timeout "http://$host:$port$path" > /dev/null 2>&1; then
            log_success "$service_name 服务正常"
            return 0
        else
            log_error "$service_name 服务连接失败"
            return 1
        fi
    else
        # 使用 nc 检查端口
        if nc -z -w $timeout $host $port 2>/dev/null; then
            log_success "$service_name 端口 $port 可访问"
            return 0
        else
            log_error "$service_name 端口 $port 不可访问"
            return 1
        fi
    fi
}

# 检查 Pod 状态
check_pod_status() {
    log_info "检查 Pod 状态..."
    
    if ! podman pod exists refly-pod 2>/dev/null; then
        log_error "Pod 不存在"
        return 1
    fi
    
    local pod_status=$(podman pod ps --filter name=refly-pod --format "{{.Status}}" 2>/dev/null)
    
    if [[ "$pod_status" == *"Running"* ]]; then
        log_success "Pod 状态: $pod_status"
    else
        log_error "Pod 状态异常: $pod_status"
        return 1
    fi
    
    # 检查容器状态
    log_info "检查容器状态..."
    local containers=$(podman ps --filter pod=refly-pod --format "{{.Names}}\t{{.Status}}")
    
    if [ -z "$containers" ]; then
        log_error "没有找到运行中的容器"
        return 1
    fi
    
    echo "$containers" | while IFS=$'\t' read -r name status; do
        if [[ "$status" == *"Up"* ]]; then
            log_success "容器 $name: $status"
        else
            log_error "容器 $name 状态异常: $status"
        fi
    done
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 检查 PostgreSQL
    if podman exec refly-pod-postgres pg_isready -U refly -d refly > /dev/null 2>&1; then
        log_success "PostgreSQL 数据库连接正常"
    else
        log_error "PostgreSQL 数据库连接失败"
        return 1
    fi
    
    # 检查 Redis
    if podman exec refly-pod-redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis 连接正常"
    else
        log_error "Redis 连接失败"
        return 1
    fi
}

# 检查存储服务
check_storage() {
    log_info "检查存储服务..."
    
    # 检查 MinIO
    if check_service "MinIO API" "localhost" "39000" "/minio/health/live" 10; then
        log_success "MinIO API 服务正常"
    else
        log_error "MinIO API 服务异常"
        return 1
    fi
    
    # 检查 Qdrant
    if check_service "Qdrant" "localhost" "36333" "/healthz" 10; then
        log_success "Qdrant 服务正常"
    else
        log_error "Qdrant 服务异常"
        return 1
    fi
}

# 检查应用服务
check_application() {
    log_info "检查应用服务..."
    
    # 检查 SearXNG
    if check_service "SearXNG" "localhost" "38080" "/" 10; then
        log_success "SearXNG 服务正常"
    else
        log_warning "SearXNG 服务可能未完全启动"
    fi
    
    # 检查 API 服务
    if check_service "Refly API" "localhost" "5800" "/" 15; then
        log_success "Refly API 服务正常"
    else
        log_error "Refly API 服务异常"
        return 1
    fi
    
    # 检查 Web 服务
    if check_service "Refly Web" "localhost" "5700" "/" 10; then
        log_success "Refly Web 服务正常"
    else
        log_error "Refly Web 服务异常"
        return 1
    fi
}

# 检查端口映射
check_ports() {
    log_info "检查端口映射..."
    
    local expected_ports=("5700" "5800" "5801" "35432" "36379" "38001" "39000" "39001" "36333" "38080")
    local failed_ports=()
    
    for port in "${expected_ports[@]}"; do
        if nc -z localhost $port 2>/dev/null; then
            log_success "端口 $port 可访问"
        else
            log_warning "端口 $port 不可访问"
            failed_ports+=($port)
        fi
    done
    
    if [ ${#failed_ports[@]} -gt 0 ]; then
        log_warning "以下端口不可访问: ${failed_ports[*]}"
        log_info "这可能是正常的，某些服务可能需要更多时间启动"
    fi
}

# 生成验证报告
generate_report() {
    log_info "生成验证报告..."
    
    local report_file="/tmp/refly-pod-verification-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "Refly Pod 部署验证报告"
        echo "生成时间: $(date)"
        echo "================================"
        echo ""
        
        echo "Pod 状态:"
        podman pod ps --filter name=refly-pod 2>/dev/null || echo "无法获取 Pod 状态"
        echo ""
        
        echo "容器状态:"
        podman ps --filter pod=refly-pod --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "无法获取容器状态"
        echo ""
        
        echo "端口检查:"
        local ports=("5700:Web" "5800:API" "5801:API-WS" "39000:MinIO" "39001:MinIO-Console" "36333:Qdrant" "38080:SearXNG")
        for port_info in "${ports[@]}"; do
            IFS=':' read -r port service <<< "$port_info"
            if nc -z localhost $port 2>/dev/null; then
                echo "  ✓ $service ($port): 可访问"
            else
                echo "  ✗ $service ($port): 不可访问"
            fi
        done
        echo ""
        
        echo "访问地址:"
        echo "  Web 界面: http://localhost:5700"
        echo "  API 服务: http://localhost:5800"
        echo "  MinIO 控制台: http://localhost:39001"
        echo "  Redis Insight: http://localhost:38001"
        echo "  SearXNG: http://localhost:38080"
        
    } > "$report_file"
    
    log_success "验证报告已保存到: $report_file"
}

# 主验证函数
main() {
    log_info "开始验证 Refly Pod 部署..."
    echo ""
    
    local failed_checks=0
    
    # 执行各项检查
    check_pod_status || ((failed_checks++))
    echo ""
    
    check_database || ((failed_checks++))
    echo ""
    
    check_storage || ((failed_checks++))
    echo ""
    
    check_application || ((failed_checks++))
    echo ""
    
    check_ports
    echo ""
    
    generate_report
    echo ""
    
    # 总结
    if [ $failed_checks -eq 0 ]; then
        log_success "所有检查通过！Refly Pod 部署成功"
        echo ""
        log_info "您现在可以访问:"
        echo "  🌐 Web 界面: http://localhost:5700"
        echo "  🔧 API 文档: http://localhost:5800"
        echo "  📊 MinIO 控制台: http://localhost:39001 (minioadmin/minioadmin)"
        echo "  🔍 Redis Insight: http://localhost:38001"
    else
        log_warning "发现 $failed_checks 个问题，请检查日志"
        log_info "常见解决方案:"
        echo "  1. 等待更长时间让服务完全启动"
        echo "  2. 检查容器日志: podman logs refly-pod-<container-name>"
        echo "  3. 重启 Pod: ./manage-refly-pod.sh restart"
    fi
}

# 执行主函数
main "$@"
