# Refly 简化 Pod 配置
# 适用于 Podman 的单 Pod 部署

apiVersion: v1
kind: Pod
metadata:
  name: refly-pod
  labels:
    app: refly
spec:
  restartPolicy: Always
  
  # 共享卷配置
  volumes:
  - name: postgres-data
    hostPath:
      path: /tmp/refly-pod/postgres-data
      type: DirectoryOrCreate
  - name: redis-data
    hostPath:
      path: /tmp/refly-pod/redis-data
      type: DirectoryOrCreate
  - name: minio-data
    hostPath:
      path: /tmp/refly-pod/minio-data
      type: DirectoryOrCreate
  - name: qdrant-data
    hostPath:
      path: /tmp/refly-pod/qdrant-data
      type: DirectoryOrCreate

  containers:
  # PostgreSQL 数据库
  - name: postgres
    image: docker.io/library/postgres:16-alpine
    ports:
    - containerPort: 5432
      hostPort: 35432
    env:
    - name: POSTGRES_DB
      value: "refly"
    - name: POSTGRES_USER
      value: "refly"
    - name: POSTGRES_PASSWORD
      value: "test"
    - name: PGDATA
      value: "/var/lib/postgresql/data/pgdata"
    volumeMounts:
    - name: postgres-data
      mountPath: /var/lib/postgresql/data
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"

  # Redis 缓存
  - name: redis
    image: docker.io/redis/redis-stack:latest
    ports:
    - containerPort: 6379
      hostPort: 36379
    - containerPort: 8001
      hostPort: 38001
    volumeMounts:
    - name: redis-data
      mountPath: /data
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "300m"

  # MinIO 对象存储
  - name: minio
    image: docker.io/minio/minio:RELEASE.2025-01-20T14-49-07Z
    command:
    - minio
    - server
    - /data
    - --console-address
    - ":9001"
    ports:
    - containerPort: 9000
      hostPort: 39000
    - containerPort: 9001
      hostPort: 39001
    env:
    - name: MINIO_ROOT_USER
      value: "minioadmin"
    - name: MINIO_ROOT_PASSWORD
      value: "minioadmin"
    volumeMounts:
    - name: minio-data
      mountPath: /data
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"

  # Qdrant 向量数据库
  - name: qdrant
    image: docker.io/reflyai/qdrant:v1.13.1
    ports:
    - containerPort: 6333
      hostPort: 36333
    - containerPort: 6334
      hostPort: 36334
    volumeMounts:
    - name: qdrant-data
      mountPath: /qdrant/storage
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"

  # SearXNG 搜索引擎
  - name: searxng
    image: docker.io/searxng/searxng:latest
    ports:
    - containerPort: 8080
      hostPort: 38080
    env:
    - name: SEARXNG_BASE_URL
      value: "http://localhost:8080/"
    - name: UWSGI_WORKERS
      value: "2"
    - name: UWSGI_THREADS
      value: "2"
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"

  # Refly API 服务
  - name: api
    image: docker.io/reflyai/refly-api:latest
    ports:
    - containerPort: 5800
      hostPort: 5800
    - containerPort: 5801
      hostPort: 5801
    env:
    - name: NODE_ENV
      value: "production"
    - name: PORT
      value: "5800"
    - name: WS_PORT
      value: "5801"
    - name: ORIGIN
      value: "http://localhost:5700"
    - name: AUTO_MIGRATE_DB_SCHEMA
      value: "1"
    - name: DATABASE_URL
      value: "postgresql://refly:test@localhost:5432/refly?schema=refly"
    - name: REDIS_HOST
      value: "localhost"
    - name: REDIS_PORT
      value: "6379"
    - name: MINIO_INTERNAL_ENDPOINT
      value: "localhost"
    - name: MINIO_INTERNAL_PORT
      value: "9000"
    - name: MINIO_INTERNAL_USE_SSL
      value: "false"
    - name: MINIO_INTERNAL_ACCESS_KEY
      value: "minioadmin"
    - name: MINIO_INTERNAL_SECRET_KEY
      value: "minioadmin"
    - name: MINIO_INTERNAL_BUCKET
      value: "refly-internal"
    - name: MINIO_EXTERNAL_ENDPOINT
      value: "localhost"
    - name: MINIO_EXTERNAL_PORT
      value: "9000"
    - name: MINIO_EXTERNAL_USE_SSL
      value: "false"
    - name: MINIO_EXTERNAL_ACCESS_KEY
      value: "minioadmin"
    - name: MINIO_EXTERNAL_SECRET_KEY
      value: "minioadmin"
    - name: MINIO_EXTERNAL_BUCKET
      value: "refly-external"
    - name: QDRANT_HOST
      value: "localhost"
    - name: QDRANT_PORT
      value: "6333"
    - name: SEARXNG_BASE_URL
      value: "http://localhost:8080"
    - name: JWT_SECRET
      value: "C0mpl3xR@nd0mS3cr3t!2023"
    - name: JWT_EXPIRATION_TIME
      value: "6h"
    - name: JWT_REFRESH_EXPIRATION_TIME
      value: "7d"
    - name: STATIC_PUBLIC_ENDPOINT
      value: "/api/v1/misc/public"
    - name: STATIC_PRIVATE_ENDPOINT
      value: "/api/v1/misc"
    - name: EMBEDDINGS_PROVIDER
      value: "jina"
    - name: EMBEDDINGS_MODEL_NAME
      value: "jina-embeddings-v3"
    - name: AUTH_SKIP_VERIFICATION
      value: "true"
    resources:
      requests:
        memory: "1Gi"
        cpu: "500m"
      limits:
        memory: "2Gi"
        cpu: "1000m"

  # Refly Web 前端
  - name: web
    image: docker.io/reflyai/refly-web:latest
    ports:
    - containerPort: 80
      hostPort: 5700
    env:
    - name: API_URL
      value: "/api"
    - name: COLLAB_URL
      value: "/collab"
    - name: STATIC_PUBLIC_ENDPOINT
      value: "/api/v1/misc/public"
    - name: STATIC_PRIVATE_ENDPOINT
      value: "/api/v1/misc"
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"
