# Refly Kubernetes Pod 部署方案

本目录包含使用 Podman 的 Kubernetes 集成功能部署 Refly 项目的完整方案。

## 📋 概述

Refly 是一个 AI 工作空间项目，包含以下组件：
- **Web 前端**: React 应用，提供用户界面
- **API 服务**: Node.js 后端服务
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储
- **MinIO**: 对象存储服务
- **Qdrant**: 向量数据库
- **SearXNG**: 搜索引擎服务

## 🚀 快速开始

### 前提条件

1. **Podman** 已安装并配置
2. **已拉取的 Docker 镜像**:
   - `docker.io/reflyai/refly-api:latest`
   - `docker.io/reflyai/refly-web:latest`
   - `docker.io/library/postgres:16-alpine`
   - `docker.io/redis/redis-stack:latest`
   - `docker.io/minio/minio:RELEASE.2025-01-20T14-49-07Z`
   - `docker.io/reflyai/qdrant:v1.13.1`
   - `docker.io/searxng/searxng:latest`

### 一键部署

```bash
# 进入项目目录
cd /path/to/refly-main

# 运行简化部署脚本（推荐）
./deploy/pod-k8s/deploy-simple.sh
```

## 📁 文件说明

### 配置文件

- **`refly-simple-pod.yaml`**: 简化的 Pod 配置文件（推荐使用）
- **`refly-pod-complete.yaml`**: 完整的 Kubernetes 配置文件（包含 PVC、Service 等）

### 脚本文件

- **`deploy-simple.sh`**: 简化部署脚本（推荐使用）
- **`deploy-refly-pod.sh`**: 完整部署脚本
- **`manage-refly-pod.sh`**: Pod 管理脚本
- **`verify-deployment.sh`**: 部署验证脚本

## 🔧 部署选项

### 选项 1: 简化部署（推荐）

使用 `refly-simple-pod.yaml` 和 `deploy-simple.sh`：

```bash
./deploy/pod-k8s/deploy-simple.sh
```

**特点**:
- 单个 Pod 包含所有服务
- 使用 hostPath 存储
- 配置简单，适合开发和测试

### 选项 2: 完整部署

使用 `refly-pod-complete.yaml` 和 `deploy-refly-pod.sh`：

```bash
./deploy/pod-k8s/deploy-refly-pod.sh
```

**特点**:
- 包含 PVC、ConfigMap、Service 等完整资源
- 更接近生产环境配置
- 配置复杂，适合生产环境

## 🌐 服务访问

部署成功后，可以通过以下地址访问服务：

| 服务 | 地址 | 说明 |
|------|------|------|
| **Web 界面** | http://localhost:5700 | 主要用户界面 |
| **API 服务** | http://localhost:5800 | REST API 接口 |
| **API WebSocket** | ws://localhost:5801 | WebSocket 连接 |
| **MinIO 控制台** | http://localhost:39001 | 对象存储管理 (minioadmin/minioadmin) |
| **Redis Insight** | http://localhost:38001 | Redis 管理界面 |
| **SearXNG** | http://localhost:38080 | 搜索引擎界面 |

### 数据库连接

| 数据库 | 地址 | 凭据 |
|--------|------|------|
| **PostgreSQL** | localhost:35432 | refly/test |
| **Redis** | localhost:36379 | 无密码 |
| **Qdrant** | localhost:36333 | 无认证 |

## 🛠️ 管理命令

### 基本管理

```bash
# 查看 Pod 状态
podman pod ps --filter name=refly-pod

# 查看容器状态
podman ps --filter pod=refly-pod

# 查看日志
podman logs -f refly-pod-<container-name>

# 停止 Pod
podman pod stop refly-pod

# 启动 Pod
podman pod start refly-pod

# 删除 Pod
podman pod rm refly-pod
```

### 使用管理脚本

```bash
# 查看帮助
./deploy/pod-k8s/manage-refly-pod.sh help

# 查看状态
./deploy/pod-k8s/manage-refly-pod.sh status

# 重启服务
./deploy/pod-k8s/manage-refly-pod.sh restart

# 查看日志
./deploy/pod-k8s/manage-refly-pod.sh logs api

# 进入容器
./deploy/pod-k8s/manage-refly-pod.sh shell api
```

## 🔍 故障排除

### 验证部署

```bash
./deploy/pod-k8s/verify-deployment.sh
```

### 常见问题

1. **服务启动慢**
   - 等待更长时间，某些服务需要较长启动时间
   - 检查容器日志：`podman logs refly-pod-<container-name>`

2. **端口冲突**
   - 确保所需端口未被占用
   - 修改配置文件中的 hostPort 设置

3. **存储权限问题**
   - 检查 `/tmp/refly-pod/` 目录权限
   - 运行：`chmod -R 755 /tmp/refly-pod/`

4. **镜像拉取失败**
   - 确保所有必需镜像已拉取
   - 运行：`podman images | grep refly`

### 日志查看

```bash
# 查看所有容器日志
podman logs refly-pod

# 查看特定容器日志
podman logs refly-pod-api
podman logs refly-pod-web
podman logs refly-pod-postgres
podman logs refly-pod-redis
podman logs refly-pod-minio
podman logs refly-pod-qdrant
podman logs refly-pod-searxng
```

## 📊 资源使用

### 默认资源限制

| 服务 | CPU 请求 | 内存请求 | CPU 限制 | 内存限制 |
|------|----------|----------|----------|----------|
| PostgreSQL | 200m | 256Mi | 500m | 1Gi |
| Redis | 100m | 128Mi | 300m | 512Mi |
| MinIO | 200m | 256Mi | 500m | 512Mi |
| Qdrant | 200m | 256Mi | 500m | 1Gi |
| SearXNG | 100m | 128Mi | 200m | 256Mi |
| API | 500m | 1Gi | 1000m | 2Gi |
| Web | 200m | 256Mi | 500m | 512Mi |

### 存储使用

- **PostgreSQL**: `/tmp/refly-pod/postgres-data`
- **Redis**: `/tmp/refly-pod/redis-data`
- **MinIO**: `/tmp/refly-pod/minio-data`
- **Qdrant**: `/tmp/refly-pod/qdrant-data`

## 🔄 升级和维护

### 更新镜像

```bash
# 拉取最新镜像
podman pull docker.io/reflyai/refly-api:latest
podman pull docker.io/reflyai/refly-web:latest

# 重新部署
./deploy/pod-k8s/deploy-simple.sh
```

### 备份数据

```bash
# 备份数据目录
tar -czf refly-backup-$(date +%Y%m%d).tar.gz /tmp/refly-pod/
```

## 📝 注意事项

1. **开发环境**: 此配置适合开发和测试环境
2. **数据持久化**: 数据存储在 `/tmp/refly-pod/` 目录中
3. **网络**: 所有服务在同一 Pod 中，共享网络命名空间
4. **安全**: 使用默认密码，生产环境请修改
5. **性能**: 资源限制可根据实际需求调整

## 🆘 获取帮助

如果遇到问题，请：

1. 查看日志文件
2. 运行验证脚本
3. 检查 Podman 状态
4. 参考故障排除部分

---

**祝您使用愉快！** 🎉
