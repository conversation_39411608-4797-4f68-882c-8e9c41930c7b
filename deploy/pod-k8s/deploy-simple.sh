#!/bin/bash

# Refly 简化 Pod 部署脚本
# 使用 Podman 部署单个 Pod

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v podman &> /dev/null; then
        log_error "Podman 未安装，请先安装 Podman"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建存储目录
create_storage_dirs() {
    log_info "创建存储目录..."
    
    local base_dir="/tmp/refly-pod"
    local dirs=("postgres-data" "redis-data" "minio-data" "qdrant-data")
    
    for dir in "${dirs[@]}"; do
        local full_path="${base_dir}/${dir}"
        if [ ! -d "$full_path" ]; then
            mkdir -p "$full_path"
            log_info "创建目录: $full_path"
        fi
    done
    
    # 设置权限
    chmod -R 755 "$base_dir"
    
    log_success "存储目录创建完成"
}

# 清理现有资源
cleanup_existing() {
    log_info "清理现有资源..."
    
    # 停止并删除现有的 Pod
    if podman pod exists refly-pod 2>/dev/null; then
        log_warning "发现现有的 refly-pod，正在删除..."
        podman pod stop refly-pod 2>/dev/null || true
        podman pod rm refly-pod 2>/dev/null || true
    fi
    
    # 清理 Kubernetes 资源
    if podman play kube --down deploy/pod-k8s/refly-simple-pod.yaml 2>/dev/null; then
        log_info "清理现有 Kubernetes 资源"
    fi
    
    log_success "资源清理完成"
}

# 部署 Pod
deploy_pod() {
    log_info "开始部署 Refly Pod..."
    
    local config_file="deploy/pod-k8s/refly-simple-pod.yaml"
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 使用 podman play kube 部署
    log_info "使用 Podman 部署 Kubernetes YAML..."
    if podman play kube "$config_file"; then
        log_success "Pod 部署成功"
    else
        log_error "Pod 部署失败"
        exit 1
    fi
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    local max_wait=180  # 最大等待时间（秒）
    local wait_time=0
    local check_interval=10
    
    while [ $wait_time -lt $max_wait ]; do
        log_info "检查服务状态... (${wait_time}s/${max_wait}s)"
        
        # 检查 Pod 状态
        local pod_status=$(podman pod ps --filter name=refly-pod --format "{{.Status}}" 2>/dev/null || echo "")
        
        if [[ "$pod_status" == *"Running"* ]]; then
            log_success "Pod 正在运行"
            break
        fi
        
        sleep $check_interval
        wait_time=$((wait_time + check_interval))
    done
    
    if [ $wait_time -ge $max_wait ]; then
        log_error "服务启动超时"
        return 1
    fi
    
    # 额外等待服务完全启动
    log_info "等待服务完全启动..."
    sleep 30
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    # 检查 Pod 状态
    log_info "Pod 状态:"
    podman pod ps --filter name=refly-pod
    
    # 检查容器状态
    log_info "容器状态:"
    podman ps --filter pod=refly-pod
    
    log_success "部署验证完成"
}

# 显示访问信息
show_access_info() {
    log_info "服务访问信息:"
    echo ""
    echo "🌐 Web 界面:"
    echo "   http://localhost:5700"
    echo ""
    echo "🔧 API 服务:"
    echo "   HTTP: http://localhost:5800"
    echo "   WebSocket: ws://localhost:5801"
    echo ""
    echo "📊 管理界面:"
    echo "   MinIO 控制台: http://localhost:39001 (minioadmin/minioadmin)"
    echo "   Redis Insight: http://localhost:38001"
    echo ""
    echo "🔍 搜索服务:"
    echo "   SearXNG: http://localhost:38080"
    echo ""
    echo "🗄️ 数据库连接:"
    echo "   PostgreSQL: localhost:35432 (refly/test)"
    echo "   Redis: localhost:36379"
    echo "   Qdrant: localhost:36333"
    echo ""
    echo "📝 管理命令:"
    echo "   查看日志: podman logs -f refly-pod-<container-name>"
    echo "   停止服务: podman pod stop refly-pod"
    echo "   启动服务: podman pod start refly-pod"
    echo "   删除服务: podman pod rm refly-pod"
    echo "   管理脚本: ./manage-refly-pod.sh"
    echo ""
}

# 主函数
main() {
    log_info "开始部署 Refly Pod (简化版)..."
    echo ""
    
    check_dependencies
    create_storage_dirs
    cleanup_existing
    deploy_pod
    wait_for_services
    verify_deployment
    show_access_info
    
    log_success "Refly Pod 部署完成！"
    log_info "如需验证部署，请运行: ./verify-deployment.sh"
}

# 处理中断信号
trap 'log_error "部署被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
