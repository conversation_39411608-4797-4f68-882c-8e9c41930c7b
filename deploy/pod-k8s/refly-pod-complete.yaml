# Refly 完整 Pod 部署配置
# 包含所有必要的资源：Namespace, PVC, ConfigMap, Pod, Service

---
# Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: refly-pod
  labels:
    name: refly-pod

---
# PersistentVolumes
apiVersion: v1
kind: PersistentVolume
metadata:
  name: refly-postgres-pv
  namespace: refly-pod
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  hostPath:
    path: /tmp/refly-pod/postgres-data
    type: DirectoryOrCreate

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: refly-redis-pv
  namespace: refly-pod
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  hostPath:
    path: /tmp/refly-pod/redis-data
    type: DirectoryOrCreate

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: refly-minio-pv
  namespace: refly-pod
spec:
  capacity:
    storage: 20Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  hostPath:
    path: /tmp/refly-pod/minio-data
    type: DirectoryOrCreate

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: refly-qdrant-pv
  namespace: refly-pod
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  hostPath:
    path: /tmp/refly-pod/qdrant-data
    type: DirectoryOrCreate

---
# PersistentVolumeClaims
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: refly-postgres-pvc
  namespace: refly-pod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-storage

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: refly-redis-pvc
  namespace: refly-pod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: local-storage

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: refly-minio-pvc
  namespace: refly-pod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: local-storage

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: refly-qdrant-pvc
  namespace: refly-pod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-storage

---
# SearXNG ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: searxng-config
  namespace: refly-pod
data:
  settings.yml: |
    use_default_settings: true
    server:
      port: 8080
      bind_address: "0.0.0.0"
      secret_key: "refly-searxng-secret-key"
    search:
      safe_search: 0
      autocomplete: "google"
    ui:
      default_locale: "zh-CN"
      theme_args:
        simple_style: "dark"

---
# Refly Pod
apiVersion: v1
kind: Pod
metadata:
  name: refly-pod
  namespace: refly-pod
  labels:
    app: refly
    version: "0.6.0"
spec:
  restartPolicy: Always
  
  # 初始化容器 - 创建必要的目录
  initContainers:
  - name: init-directories
    image: docker.io/library/busybox:latest
    command: ['sh', '-c']
    args:
    - |
      mkdir -p /postgres-data /redis-data /minio-data /qdrant-data
      chown -R 999:999 /postgres-data
      chown -R 999:999 /redis-data
      chown -R 1000:1000 /minio-data
      chown -R 1000:1000 /qdrant-data
    volumeMounts:
    - name: postgres-storage
      mountPath: /postgres-data
    - name: redis-storage
      mountPath: /redis-data
    - name: minio-storage
      mountPath: /minio-data
    - name: qdrant-storage
      mountPath: /qdrant-data

  containers:
  # PostgreSQL 数据库
  - name: postgres
    image: docker.io/library/postgres:16-alpine
    ports:
    - containerPort: 5432
      name: postgres
    env:
    - name: POSTGRES_DB
      value: "refly"
    - name: POSTGRES_USER
      value: "refly"
    - name: POSTGRES_PASSWORD
      value: "test"
    - name: PGDATA
      value: "/var/lib/postgresql/data/pgdata"
    volumeMounts:
    - name: postgres-storage
      mountPath: /var/lib/postgresql/data
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    livenessProbe:
      exec:
        command:
        - pg_isready
        - -U
        - refly
        - -d
        - refly
      initialDelaySeconds: 30
      periodSeconds: 10
      timeoutSeconds: 5
    readinessProbe:
      exec:
        command:
        - pg_isready
        - -U
        - refly
        - -d
        - refly
      initialDelaySeconds: 10
      periodSeconds: 5
      timeoutSeconds: 3

  # Redis 缓存
  - name: redis
    image: docker.io/redis/redis-stack:latest
    ports:
    - containerPort: 6379
      name: redis
    - containerPort: 8001
      name: redis-insight
    volumeMounts:
    - name: redis-storage
      mountPath: /data
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "300m"
    livenessProbe:
      exec:
        command:
        - redis-cli
        - ping
      initialDelaySeconds: 10
      periodSeconds: 10
      timeoutSeconds: 3
    readinessProbe:
      exec:
        command:
        - redis-cli
        - ping
      initialDelaySeconds: 5
      periodSeconds: 5
      timeoutSeconds: 2

  # MinIO 对象存储
  - name: minio
    image: docker.io/minio/minio:RELEASE.2025-01-20T14-49-07Z
    command:
    - minio
    - server
    - /data
    - --console-address
    - ":9001"
    ports:
    - containerPort: 9000
      name: minio-api
    - containerPort: 9001
      name: minio-console
    env:
    - name: MINIO_ROOT_USER
      value: "minioadmin"
    - name: MINIO_ROOT_PASSWORD
      value: "minioadmin"
    volumeMounts:
    - name: minio-storage
      mountPath: /data
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"
    livenessProbe:
      httpGet:
        path: /minio/health/live
        port: 9000
      initialDelaySeconds: 30
      periodSeconds: 20
      timeoutSeconds: 10
    readinessProbe:
      httpGet:
        path: /minio/health/ready
        port: 9000
      initialDelaySeconds: 10
      periodSeconds: 10
      timeoutSeconds: 5

  # Qdrant 向量数据库
  - name: qdrant
    image: docker.io/reflyai/qdrant:v1.13.1
    ports:
    - containerPort: 6333
      name: qdrant-http
    - containerPort: 6334
      name: qdrant-grpc
    volumeMounts:
    - name: qdrant-storage
      mountPath: /qdrant/storage
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    livenessProbe:
      httpGet:
        path: /healthz
        port: 6333
      initialDelaySeconds: 30
      periodSeconds: 20
      timeoutSeconds: 10
    readinessProbe:
      httpGet:
        path: /healthz
        port: 6333
      initialDelaySeconds: 10
      periodSeconds: 10
      timeoutSeconds: 5

  # SearXNG 搜索引擎
  - name: searxng
    image: docker.io/searxng/searxng:latest
    ports:
    - containerPort: 8080
      name: searxng
    env:
    - name: SEARXNG_BASE_URL
      value: "http://localhost:8080/"
    - name: UWSGI_WORKERS
      value: "2"
    - name: UWSGI_THREADS
      value: "2"
    volumeMounts:
    - name: searxng-config
      mountPath: /etc/searxng/settings.yml
      subPath: settings.yml
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
    livenessProbe:
      httpGet:
        path: /
        port: 8080
      initialDelaySeconds: 30
      periodSeconds: 20
      timeoutSeconds: 10
    readinessProbe:
      httpGet:
        path: /
        port: 8080
      initialDelaySeconds: 10
      periodSeconds: 10
      timeoutSeconds: 5



  # Refly API 服务
  - name: api
    image: docker.io/reflyai/refly-api:latest
    ports:
    - containerPort: 5800
      name: api-http
    - containerPort: 5801
      name: api-ws
    env:
    - name: NODE_ENV
      value: "production"
    - name: PORT
      value: "5800"
    - name: WS_PORT
      value: "5801"
    - name: ORIGIN
      value: "http://localhost:5700"
    - name: AUTO_MIGRATE_DB_SCHEMA
      value: "1"
    - name: DATABASE_URL
      value: "postgresql://refly:test@localhost:5432/refly?schema=refly"
    - name: REDIS_HOST
      value: "localhost"
    - name: REDIS_PORT
      value: "6379"
    - name: MINIO_INTERNAL_ENDPOINT
      value: "localhost"
    - name: MINIO_INTERNAL_PORT
      value: "9000"
    - name: MINIO_INTERNAL_USE_SSL
      value: "false"
    - name: MINIO_INTERNAL_ACCESS_KEY
      value: "minioadmin"
    - name: MINIO_INTERNAL_SECRET_KEY
      value: "minioadmin"
    - name: MINIO_INTERNAL_BUCKET
      value: "refly-internal"
    - name: MINIO_EXTERNAL_ENDPOINT
      value: "localhost"
    - name: MINIO_EXTERNAL_PORT
      value: "9000"
    - name: MINIO_EXTERNAL_USE_SSL
      value: "false"
    - name: MINIO_EXTERNAL_ACCESS_KEY
      value: "minioadmin"
    - name: MINIO_EXTERNAL_SECRET_KEY
      value: "minioadmin"
    - name: MINIO_EXTERNAL_BUCKET
      value: "refly-external"
    - name: QDRANT_HOST
      value: "localhost"
    - name: QDRANT_PORT
      value: "6333"
    - name: SEARXNG_BASE_URL
      value: "http://localhost:8080"
    - name: JWT_SECRET
      value: "C0mpl3xR@nd0mS3cr3t!2023"
    - name: JWT_EXPIRATION_TIME
      value: "6h"
    - name: JWT_REFRESH_EXPIRATION_TIME
      value: "7d"
    - name: STATIC_PUBLIC_ENDPOINT
      value: "/api/v1/misc/public"
    - name: STATIC_PRIVATE_ENDPOINT
      value: "/api/v1/misc"
    - name: EMBEDDINGS_PROVIDER
      value: "jina"
    - name: EMBEDDINGS_MODEL_NAME
      value: "jina-embeddings-v3"
    - name: AUTH_SKIP_VERIFICATION
      value: "true"
    resources:
      requests:
        memory: "1Gi"
        cpu: "500m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
    livenessProbe:
      httpGet:
        path: /
        port: 5800
      initialDelaySeconds: 60
      periodSeconds: 30
      timeoutSeconds: 10
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /
        port: 5800
      initialDelaySeconds: 30
      periodSeconds: 10
      timeoutSeconds: 5

  # Refly Web 前端
  - name: web
    image: docker.io/reflyai/refly-web:latest
    ports:
    - containerPort: 80
      name: web-http
    env:
    - name: API_URL
      value: "/api"
    - name: COLLAB_URL
      value: "/collab"
    - name: STATIC_PUBLIC_ENDPOINT
      value: "/api/v1/misc/public"
    - name: STATIC_PRIVATE_ENDPOINT
      value: "/api/v1/misc"
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"
    livenessProbe:
      httpGet:
        path: /
        port: 80
      initialDelaySeconds: 30
      periodSeconds: 30
      timeoutSeconds: 10
    readinessProbe:
      httpGet:
        path: /
        port: 80
      initialDelaySeconds: 10
      periodSeconds: 10
      timeoutSeconds: 5

  # 存储卷配置
  volumes:
  - name: postgres-storage
    persistentVolumeClaim:
      claimName: refly-postgres-pvc
  - name: redis-storage
    persistentVolumeClaim:
      claimName: refly-redis-pvc
  - name: minio-storage
    persistentVolumeClaim:
      claimName: refly-minio-pvc
  - name: qdrant-storage
    persistentVolumeClaim:
      claimName: refly-qdrant-pvc
  - name: searxng-config
    configMap:
      name: searxng-config

---
# Service for external access
apiVersion: v1
kind: Service
metadata:
  name: refly-pod-service
  namespace: refly-pod
  labels:
    app: refly
spec:
  type: NodePort
  selector:
    app: refly
  ports:
  - name: web
    port: 80
    targetPort: 80
    nodePort: 30080
    protocol: TCP
  - name: api
    port: 5800
    targetPort: 5800
    nodePort: 30800
    protocol: TCP
  - name: api-ws
    port: 5801
    targetPort: 5801
    nodePort: 30801
    protocol: TCP
  - name: minio-console
    port: 9001
    targetPort: 9001
    nodePort: 30901
    protocol: TCP
  - name: redis-insight
    port: 8001
    targetPort: 8001
    nodePort: 30802
    protocol: TCP
