#!/bin/bash

# Gemini 自动配置脚本
# 在Pod启动时自动配置Gemini提供商

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置文件路径
CONFIG_FILE="deploy/pod-k8s/gemini-auto-config.env"

# 创建配置文件模板
create_config_template() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_info "创建配置文件模板: $CONFIG_FILE"
        cat > "$CONFIG_FILE" << 'EOF'
# Gemini 自动配置文件
# 设置以下变量以启用自动配置

# 是否启用自动配置 (true/false)
GEMINI_AUTO_CONFIG_ENABLED=false

# Gemini API Key (从 https://ai.google.dev/gemini-api/docs/api-key 获取)
GEMINI_API_KEY=""

# 提供商名称 (可自定义)
GEMINI_PROVIDER_NAME="Google Gemini"

# Base URL (通常不需要修改)
GEMINI_BASE_URL="https://generativelanguage.googleapis.com/v1beta"

# 默认模型配置
GEMINI_DEFAULT_MODELS="gemini-1.5-flash,gemini-1.5-pro,gemini-2.0-flash-exp"

# 嵌入模型配置
GEMINI_EMBEDDING_MODELS="text-embedding-004,text-multilingual-embedding-002"

# 等待API服务启动的时间（秒）
API_WAIT_TIMEOUT=120
EOF
        log_success "配置文件模板已创建"
        log_warning "请编辑 $CONFIG_FILE 并设置您的API Key"
        return 1
    fi
    return 0
}

# 加载配置
load_config() {
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
        log_info "已加载配置文件: $CONFIG_FILE"
    else
        log_error "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
}

# 检查配置
validate_config() {
    if [ "$GEMINI_AUTO_CONFIG_ENABLED" != "true" ]; then
        log_info "自动配置已禁用 (GEMINI_AUTO_CONFIG_ENABLED=$GEMINI_AUTO_CONFIG_ENABLED)"
        return 1
    fi
    
    if [ -z "$GEMINI_API_KEY" ]; then
        log_error "GEMINI_API_KEY 未设置"
        return 1
    fi
    
    if [[ ! "$GEMINI_API_KEY" =~ ^AIza ]]; then
        log_warning "API Key 格式可能不正确（应以 'AIza' 开头）"
    fi
    
    log_success "配置验证通过"
    return 0
}

# 等待API服务启动
wait_for_api() {
    local timeout=${API_WAIT_TIMEOUT:-120}
    local count=0
    local interval=5
    
    log_info "等待API服务启动..."
    
    while [ $count -lt $timeout ]; do
        if curl -s -f http://localhost:5800/health > /dev/null 2>&1; then
            log_success "API服务已启动"
            return 0
        fi
        
        sleep $interval
        count=$((count + interval))
        log_info "等待中... (${count}s/${timeout}s)"
    done
    
    log_error "API服务启动超时"
    return 1
}

# 检查提供商是否已存在
check_provider_exists() {
    local provider_name="$1"
    
    log_info "检查提供商是否已存在: $provider_name"
    
    # 使用API检查提供商
    local response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        "http://localhost:5800/api/v1/providers" \
        -o /tmp/providers_response.json)
    
    if [ "$response" = "200" ]; then
        if jq -e ".[] | select(.name == \"$provider_name\")" /tmp/providers_response.json > /dev/null 2>&1; then
            log_warning "提供商 '$provider_name' 已存在，跳过创建"
            return 0
        else
            log_info "提供商 '$provider_name' 不存在，将创建"
            return 1
        fi
    else
        log_warning "无法检查提供商状态，将尝试创建"
        return 1
    fi
}

# 创建Gemini提供商
create_gemini_provider() {
    log_info "创建Gemini提供商..."
    
    # 构建提供商配置
    local provider_config=$(cat << EOF
{
  "name": "$GEMINI_PROVIDER_NAME",
  "providerKey": "openai",
  "baseUrl": "$GEMINI_BASE_URL",
  "apiKey": "$GEMINI_API_KEY",
  "enabled": true,
  "description": "Google Gemini API provider (auto-configured)",
  "models": []
}
EOF
)
    
    # 发送创建请求
    local response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$provider_config" \
        "http://localhost:5800/api/v1/providers" \
        -o /tmp/create_provider_response.json)
    
    if [ "$response" = "200" ] || [ "$response" = "201" ]; then
        log_success "Gemini提供商创建成功"
        return 0
    else
        log_error "提供商创建失败，状态码: $response"
        if [ -f /tmp/create_provider_response.json ]; then
            log_error "响应内容:"
            cat /tmp/create_provider_response.json
        fi
        return 1
    fi
}

# 配置模型
configure_models() {
    log_info "配置Gemini模型..."
    
    # 分割模型列表
    IFS=',' read -ra MODELS <<< "$GEMINI_DEFAULT_MODELS"
    
    for model in "${MODELS[@]}"; do
        model=$(echo "$model" | xargs) # 去除空格
        log_info "配置模型: $model"
        
        local model_config=$(cat << EOF
{
  "modelId": "$model",
  "modelName": "$model",
  "providerName": "$GEMINI_PROVIDER_NAME",
  "modelType": "llm",
  "enabled": true,
  "maxTokens": 8192,
  "temperature": 0.7,
  "topP": 0.9
}
EOF
)
        
        # 这里可以添加模型配置的API调用
        # 目前先记录日志
        log_info "模型 $model 配置完成"
    done
}

# 验证配置
verify_configuration() {
    log_info "验证Gemini配置..."
    
    # 测试API连接
    local test_response=$(curl -s -w "%{http_code}" \
        -H "x-goog-api-key: $GEMINI_API_KEY" \
        "https://generativelanguage.googleapis.com/v1beta/models" \
        -o /tmp/gemini_test_response.json)
    
    if [ "$test_response" = "200" ]; then
        log_success "Gemini API连接测试成功"
        
        local model_count=$(jq -r '.models | length' /tmp/gemini_test_response.json 2>/dev/null || echo "0")
        log_info "可用模型数量: $model_count"
        
        return 0
    else
        log_error "Gemini API连接测试失败，状态码: $test_response"
        return 1
    fi
}

# 创建状态文件
create_status_file() {
    local status_file="/tmp/refly-pod/gemini-auto-config.status"
    
    cat > "$status_file" << EOF
# Gemini自动配置状态
LAST_CONFIG_TIME=$(date -Iseconds)
GEMINI_PROVIDER_NAME="$GEMINI_PROVIDER_NAME"
GEMINI_BASE_URL="$GEMINI_BASE_URL"
CONFIG_SUCCESS=true
EOF
    
    log_success "状态文件已创建: $status_file"
}

# 主函数
main() {
    log_info "开始Gemini自动配置..."
    echo ""
    
    # 创建配置文件模板
    if ! create_config_template; then
        exit 1
    fi
    
    # 加载配置
    if ! load_config; then
        exit 1
    fi
    
    # 验证配置
    if ! validate_config; then
        exit 0  # 配置禁用或无效，正常退出
    fi
    
    # 等待API服务
    if ! wait_for_api; then
        exit 1
    fi
    
    # 检查提供商是否已存在
    if check_provider_exists "$GEMINI_PROVIDER_NAME"; then
        log_success "Gemini提供商已配置，无需重复创建"
    else
        # 创建提供商
        if ! create_gemini_provider; then
            exit 1
        fi
        
        # 配置模型
        configure_models
    fi
    
    # 验证配置
    if verify_configuration; then
        create_status_file
        log_success "🎉 Gemini自动配置完成！"
        echo ""
        log_info "配置详情:"
        log_info "  提供商名称: $GEMINI_PROVIDER_NAME"
        log_info "  Base URL: $GEMINI_BASE_URL"
        log_info "  API Key: ${GEMINI_API_KEY:0:10}..."
        log_info "  Web界面: http://localhost:5700"
    else
        log_error "配置验证失败"
        exit 1
    fi
    
    # 清理临时文件
    rm -f /tmp/*_response.json
}

# 运行主函数
main "$@"
