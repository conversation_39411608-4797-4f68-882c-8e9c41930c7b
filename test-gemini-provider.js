#!/usr/bin/env node

/**
 * 测试Google Gemini供应商配置
 * 验证修改后的代码是否能正确识别和处理Google Gemini供应商
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 测试Google Gemini供应商配置...\n');

// 测试1: 检查供应商列表中是否包含Gemini
console.log('📋 测试1: 检查供应商列表配置');
try {
  const providerPath = path.join(__dirname, 'packages/utils/src/provider.ts');
  const providerContent = fs.readFileSync(providerPath, 'utf8');
  
  if (providerContent.includes("key: 'gemini'")) {
    console.log('✅ 供应商列表中已包含Gemini配置');
  } else {
    console.log('❌ 供应商列表中未找到Gemini配置');
  }
  
  if (providerContent.includes("name: 'Google Gemini'")) {
    console.log('✅ Gemini供应商名称配置正确');
  } else {
    console.log('❌ Gemini供应商名称配置错误');
  }
  
  if (providerContent.includes("categories: ['llm', 'embedding']")) {
    console.log('✅ Gemini供应商类别配置正确');
  } else {
    console.log('❌ Gemini供应商类别配置错误');
  }
  
  if (providerContent.includes('generativelanguage.googleapis.com/v1beta')) {
    console.log('✅ Gemini默认Base URL配置正确');
  } else {
    console.log('❌ Gemini默认Base URL配置错误');
  }
} catch (error) {
  console.log('❌ 读取供应商配置文件失败:', error.message);
}

console.log('\n📡 测试2: 检查provider-checker认证配置');
try {
  const checkerPath = path.join(__dirname, 'packages/providers/src/provider-checker/provider-checker.ts');
  const checkerContent = fs.readFileSync(checkerPath, 'utf8');
  
  if (checkerContent.includes("config.providerKey === 'gemini'")) {
    console.log('✅ provider-checker中已添加Gemini认证支持');
  } else {
    console.log('❌ provider-checker中未找到Gemini认证支持');
  }
  
  if (checkerContent.includes("case 'gemini':")) {
    console.log('✅ provider-checker中已添加Gemini检查逻辑');
  } else {
    console.log('❌ provider-checker中未找到Gemini检查逻辑');
  }
  
  if (checkerContent.includes("'x-goog-api-key': config.apiKey")) {
    console.log('✅ Gemini认证头配置正确');
  } else {
    console.log('❌ Gemini认证头配置错误');
  }
} catch (error) {
  console.log('❌ 读取provider-checker文件失败:', error.message);
}

console.log('\n🤖 测试3: 检查LLM模型配置');
try {
  const llmPath = path.join(__dirname, 'packages/providers/src/llm/index.ts');
  const llmContent = fs.readFileSync(llmPath, 'utf8');
  
  if (llmContent.includes("case 'gemini':")) {
    console.log('✅ LLM模型创建中已添加Gemini支持');
  } else {
    console.log('❌ LLM模型创建中未找到Gemini支持');
  }
  
  if (llmContent.includes('EnhancedChatOpenAI')) {
    console.log('✅ Gemini使用EnhancedChatOpenAI类');
  } else {
    console.log('❌ Gemini未使用正确的模型类');
  }
} catch (error) {
  console.log('❌ 读取LLM配置文件失败:', error.message);
}

console.log('\n🔤 测试4: 检查嵌入模型配置');
try {
  const embeddingPath = path.join(__dirname, 'packages/providers/src/embeddings/index.ts');
  const embeddingContent = fs.readFileSync(embeddingPath, 'utf8');
  
  if (embeddingContent.includes("case 'gemini':")) {
    console.log('✅ 嵌入模型中已添加Gemini支持');
  } else {
    console.log('❌ 嵌入模型中未找到Gemini支持');
  }
  
  if (embeddingContent.includes("'x-goog-api-key': provider.apiKey")) {
    console.log('✅ Gemini嵌入模型认证配置正确');
  } else {
    console.log('❌ Gemini嵌入模型认证配置错误');
  }
} catch (error) {
  console.log('❌ 读取嵌入模型配置文件失败:', error.message);
}

console.log('\n🎯 测试总结:');
console.log('✅ Google Gemini供应商配置已完成');
console.log('✅ 支持LLM和嵌入模型');
console.log('✅ 使用正确的x-goog-api-key认证方式');
console.log('✅ 默认Base URL配置为Google API端点');

console.log('\n📝 下一步操作:');
console.log('1. 重启应用服务');
console.log('2. 在Web界面中选择"Google Gemini"类型');
console.log('3. 输入有效的Gemini API Key');
console.log('4. 测试连接');

console.log('\n🔗 获取Gemini API Key:');
console.log('   访问: https://ai.google.dev/gemini-api/docs/api-key');
